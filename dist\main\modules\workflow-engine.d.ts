import type { Workflow, AppFile, WorkflowResult } from '../../shared/types';
export declare class WorkflowEngine {
    private createdDirectories;
    private processedDirectories;
    private cleanedEmptyDirectories;
    private counterMap;
    private currentLanguage;
    private dirScanCache;
    private readonly CACHE_EXPIRY_TIME;
    private readonly MAX_CACHE_SIZE;
    private store;
    private isInterrupted;
    private currentExecution;
    constructor(language?: 'zh-CN' | 'en-US', store?: any);
    /**
     * 设置当前语言
     */
    setLanguage(language: 'zh-CN' | 'en-US'): void;
    /**
     * 翻译文本
     */
    private t;
    /**
     * 中断当前执行
     */
    interrupt(): void;
    /**
     * 重置中断状态
     */
    resetInterrupt(): void;
    /**
     * 获取当前执行状态（用于异常恢复）
     */
    getCurrentExecutionState(): typeof this.currentExecution;
    /**
     * 保存部分执行结果（用于异常中断时的记录）
     */
    private savePartialResult;
    /**
     * 清理过期的缓存条目
     */
    private cleanupCache;
    /**
     * 清理所有缓存
     */
    clearCache(): void;
    /**
     * 翻译错误信息
     */
    private translateError;
    /**
     * 验证路径安全性，防止路径遍历攻击
     */
    private validatePath;
    /**
     * 验证工作流配置
     */
    private validateWorkflowConfiguration;
    /**
     * 智能查找在工作流中至少有一个步骤能够处理的文件
     * 这个函数识别所有潜在的"入口点"，包括第一个步骤和所有使用'original'输入源的步骤
     *
     * @param files 原始用户输入的文件列表
     * @param workflow 完整的工作流配置
     * @returns 在该工作流中至少有一个步骤能够处理的文件子集
     */
    findInitiallyMatchingFiles(files: AppFile[], workflow: Workflow): AppFile[];
    /**
     * 查找特定步骤能够处理的文件
     *
     * @param files 输入文件列表
     * @param step 要检查的步骤
     * @returns 该步骤能够处理的文件列表
     */
    private findMatchingFilesForStep;
    /**
     * 验证输入文件与工作流步骤的匹配性
     * 改进逻辑：使用智能验证，考虑所有潜在的入口点
     */
    private validateWorkflowInputs;
    /**
     * 生成详细的验证错误信息
     */
    private generateDetailedValidationError;
    /**
     * 分析文件与步骤的详细匹配情况
     * 提供更精确的诊断信息
     */
    private analyzeFileStepMatching;
    /**
     * 分析文件为什么不匹配条件
     */
    private analyzeWhyFileDoesNotMatch;
    /**
     * 预览工作流执行结果
     */
    preview(files: AppFile[], workflow: Workflow): Promise<WorkflowResult>;
    /**
     * 批处理执行工作流
     */
    executeBatch(files: AppFile[], workflow: Workflow, onProgress?: (progress: {
        processed: number;
        total: number;
        currentBatch: number;
        totalBatches: number;
    }) => void): Promise<WorkflowResult>;
    /**
     * 执行工作流
     */
    execute(files: AppFile[], workflow: Workflow): Promise<WorkflowResult>;
    /**
     * 获取步骤的输入文件
     */
    private getStepInputFiles;
    /**
     * 根据处理目标过滤文件
     */
    private filterFilesByProcessTarget;
    /**
     * 理想的 processDroppedPaths 逻辑：从路径字符串数组创建 AppFile 对象数组
     * 这个函数确保每个输入路径（无论是文件还是目录）都会在结果中有对应的 AppFile 对象
     *
     * @param paths 原始路径字符串数组
     * @returns AppFile 对象数组，包含路径本身和（可选的）其内容
     */
    createAppFilesFromPaths(paths: string[]): Promise<AppFile[]>;
    /**
     * 理想的 processDroppedPaths 实现蓝图：处理用户拖拽的路径并返回工作流可处理的文件
     *
     * 这个方法体现了正确的设计原则：
     * 1. 职责分离：文件扫描和工作流验证是两个独立的步骤
     * 2. 数据保真：从路径到 AppFile 的转换过程是无损的，不受工作流配置影响
     * 3. 智能验证：只有在完整扫描后才进行工作流匹配验证
     *
     * @param paths 用户拖拽的原始路径数组
     * @param workflow 目标工作流配置
     * @returns 经过智能验证后，工作流可以处理的 AppFile 数组
     */
    processPathsWithWorkflow(paths: string[], workflow: Workflow): Promise<AppFile[]>;
    /**
     * 从指定路径加载文件或文件夹
     */
    private loadItemsFromPath;
    /**
     * 从指定路径加载文件（保持向后兼容）
     */
    private loadFilesFromPath;
    /**
     * 递归获取目录中的所有项目（文件和/或文件夹）
     */
    private getAllItemsInDirectory;
    /**
     * 递归获取目录中的所有文件（保持向后兼容）
     */
    private getAllFilesInDirectory;
    /**
     * 检查文件或文件夹权限
     */
    checkPermissions(itemPath: string, operation?: 'read' | 'write' | 'both'): Promise<boolean>;
    /**
     * 验证操作的安全性和可行性
     */
    private validateOperation;
    /**
     * 检查磁盘空间是否足够
     */
    checkDiskSpace(targetPath: string, requiredSize: number): Promise<{
        hasSpace: boolean;
        error?: string;
    }>;
    /**
     * 分类和翻译错误信息
     */
    categorizeError(error: Error, operation: string, path: string): string;
    /**
     * 统一的深度计算方法
     */
    private calculateDepth;
    /**
     * 检查文件夹是否逻辑上为空（包括只包含空子文件夹的情况）
     */
    private isLogicallyEmpty;
    /**
     * 递归计算文件夹大小的辅助函数
     */
    private calculateFolderSize;
    /**
     * 获取文件或文件夹的详细信息
     */
    private getItemInfo;
    /**
     * 生成统一的文件ID
     */
    private generateFileId;
    /**
     * 获取文件信息
     */
    private getFileInfo;
    /**
     * 预览步骤处理
     */
    private processStepPreview;
    /**
     * 执行步骤处理
     */
    private processStepExecute;
    /**
     * 检查文件是否匹配条件组
     */
    private matchesConditions;
    /**
     * 检查文件是否匹配单个条件
     */
    private matchesCondition;
    /**
     * 比较两个日期字符串
     * @param dateStr1 第一个日期字符串
     * @param dateStr2 第二个日期字符串
     * @returns 1 如果 dateStr1 > dateStr2, -1 如果 dateStr1 < dateStr2, 0 如果相等
     */
    private compareDates;
    /**
     * 获取日期字符串的日期部分（YYYY-MM-DD格式）
     * @param dateStr 日期字符串
     * @returns YYYY-MM-DD 格式的日期字符串
     */
    private getDateOnly;
    /**
     * 根据分类方式生成子文件夹路径
     */
    private generateClassificationPath;
    /**
     * 生成日期文件夹路径
     */
    private generateDateFolderPath;
    /**
     * 生成文件大小文件夹路径
     */
    private generateSizeFolderPath;
    /**
     * 转换大小单位为字节
     */
    private convertToBytes;
    /**
     * 计算相对日期
     */
    private calculateRelativeDate;
    /**
     * 匹配相对日期条件
     */
    private matchesRelativeDate;
    /**
     * 获取文件或文件夹的指定字段值
     */
    private getFileValue;
    /**
     * 计算新路径（执行用，会递增counter）
     */
    private calculateNewPathForExecution;
    /**
     * 计算新路径（预览用）
     */
    private calculateNewPath;
    /**
     * 根据分类方式生成子文件夹路径（预览用）
     */
    private generateClassificationPathForPreview;
    /**
     * 为预览生成文件名（不会递增counter）
     */
    private generateFileNameForPreview;
    /**
     * 为预览应用自定义命名模式（不会递增counter）
     */
    private applyCustomPatternForPreview;
    /**
     * 应用removeSpaces和removeSpecialChars到文件名
     */
    private applyNameCleanup;
    /**
     * 验证文件名安全性
     */
    private validateFileName;
    /**
     * 生成新文件名
     */
    private generateFileName;
    /**
     * 格式化日期
     */
    private formatDate;
    /**
     * 应用自定义命名模式
     */
    private applyCustomPattern;
    /**
     * 添加前缀
     */
    private applyPrefix;
    /**
     * 添加后缀
     */
    private applySuffix;
    /**
     * 查找替换
     */
    private applyReplace;
    /**
     * 大小写转换
     */
    private applyCase;
    /**
     * 应用高级组合规则
     */
    private applyAdvancedRules;
    /**
     * 应用大小写转换（辅助方法）
     */
    private applyCaseTransform;
    /**
     * 执行动作
     */
    private executeActions;
    /**
     * 执行单个动作
     */
    private executeAction;
    /**
     * 执行删除操作
     */
    private executeDelete;
    /**
     * 执行移动操作
     */
    private executeMove;
    /**
     * 执行复制操作
     */
    private executeCopy;
    /**
     * 清理空文件夹
     */
    private cleanupEmptyFolders;
    /**
     * 根据文件扩展名智能分类文件类型
     */
    private getFileTypeCategory;
    /**
     * 确保目录存在并跟踪创建的目录
     */
    private ensureDirWithTracking;
    /**
     * 清理工作流创建的空文件夹（软件内部清理机制）
     */
    private cleanupCreatedEmptyDirectories;
    /**
     * 清理处理过程中遇到的所有空文件夹（用户功能）
     */
    private cleanupAllProcessedEmptyDirectories;
    /**
     * 递归检查并清理父目录（如果为空）
     */
    private checkAndCleanupParentDirectory;
    /**
     * 检查目录是否为空
     */
    private isEmptyDirectory;
    /**
     * 重新扫描原始目录
     */
    private rescanOriginalDirectories;
    /**
     * 带缓存的目录扫描
     */
    private loadItemsFromPathWithCache;
    /**
     * 获取工作流执行过程中创建的文件夹列表
     */
    getCreatedDirectories(): string[];
    /**
     * 获取并保存创建的文件夹列表（用于历史记录）
     * 这个方法会在清理之前调用，确保历史记录能获取到完整的文件夹列表
     */
    getAndPreserveCreatedDirectories(): string[];
    /**
     * 清空创建的文件夹列表（在历史记录创建后调用）
     */
    clearCreatedDirectories(): void;
    /**
     * 获取被清理的空文件夹列表
     */
    getCleanedEmptyDirectories(): string[];
    /**
     * 获取并保存被清理的空文件夹列表（用于历史记录）
     */
    getAndPreserveCleanedEmptyDirectories(): string[];
    /**
     * 清空被清理空文件夹的跟踪列表（在历史记录创建后调用）
     */
    clearCleanedEmptyDirectories(): void;
    /**
     * 生成文件变化记录
     * 通过比较步骤的输入和输出文件来确定变化类型
     */
    private generateFileChanges;
}
