export interface DropGroup {
    rootPath: string;
    files: AppFile[];
}
export interface AppFile {
    id: string;
    name: string;
    path: string;
    size: number;
    type: string;
    status: 'pending' | 'success' | 'error';
    newPath?: string;
    error?: string;
    createdDate?: string;
    modifiedDate?: string;
    isDirectory?: boolean;
    originalDir?: string;
    fileCount?: number;
    folderCount?: number;
    totalSize?: number;
    depth?: number;
    isEmpty?: boolean;
}
export interface Workflow {
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    order: number;
    steps: ProcessStep[];
    createdAt: string;
    updatedAt: string;
    defaultInputPath?: string;
    cleanupEmptyFolders?: boolean;
    includeSubfolders?: boolean;
}
export interface ProcessStep {
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    order: number;
    inputSource: InputSource;
    conditions: ConditionGroup;
    actions: Action[];
    outputPath?: string;
    processTarget: 'files' | 'folders';
}
export interface InputSource {
    type: 'original' | 'previous_step' | 'specific_path';
    stepId?: string;
    path?: string;
}
export interface ConditionGroup {
    id?: string;
    operator: 'AND' | 'OR';
    conditions: Condition[];
    groups?: ConditionGroup[];
}
export interface Condition {
    id: string;
    field: 'fileName' | 'fileExtension' | 'fileSize' | 'fileType' | 'createdDate' | 'modifiedDate' | 'filePath' | 'folderName' | 'folderSize' | 'folderFileCount' | 'folderSubfolderCount' | 'folderIsEmpty' | 'itemType';
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'greaterThan' | 'lessThan' | 'greaterThanOrEqual' | 'lessThanOrEqual' | 'in' | 'notEquals' | 'notContains' | 'notStartsWith' | 'notEndsWith' | 'notIn' | 'is';
    value: string | number | string[] | boolean;
    enabled: boolean;
    sizeUnit?: 'B' | 'KB' | 'MB' | 'GB';
    dateType?: 'absolute' | 'relative';
    relativeDateValue?: number;
    relativeDateUnit?: 'days' | 'weeks' | 'months' | 'years';
    relativeDateDirection?: 'ago' | 'within';
}
export interface Action {
    id: string;
    type: 'move' | 'copy' | 'rename' | 'delete' | 'createFolder';
    enabled: boolean;
    config: ActionConfig;
}
export interface ActionConfig {
    targetPath?: string;
    targetPathType?: 'input_folder' | 'specific_path';
    createSubfolders?: boolean;
    classifyBy?: 'fileType' | 'createdDate' | 'modifiedDate' | 'fileSize' | 'extension' | 'preserveStructure';
    dateGrouping?: 'year' | 'yearMonth' | 'yearMonthDay' | 'quarter' | 'monthName';
    sizeClassifyMode?: 'preset' | 'custom';
    sizePreset?: 'general' | 'photo' | 'video';
    customSizeRanges?: FileSizeRange[];
    namingPattern?: 'original' | 'timestamp' | 'date' | 'file-created' | 'file-modified' | 'counter' | 'custom' | 'prefix' | 'suffix' | 'replace' | 'case' | 'advanced';
    dateFormat?: string;
    customPattern?: string;
    counterStart?: number;
    counterPadding?: number;
    confirmDelete?: boolean;
    prefix?: string;
    suffix?: string;
    replaceFrom?: string;
    replaceTo?: string;
    caseType?: 'lower' | 'upper' | 'title' | 'camel' | 'pascal' | 'snake' | 'kebab';
    removeSpaces?: boolean;
    removeSpecialChars?: boolean;
    advancedRules?: AdvancedNamingRule[];
    deleteEmptyFolders?: boolean;
    deleteNonEmptyFolders?: boolean;
    preserveFolderStructure?: boolean;
    processSubfolders?: boolean;
    maxDepth?: number;
}
export interface AdvancedNamingRule {
    id: string;
    type: 'prefix' | 'suffix' | 'replace' | 'case' | 'counter' | 'date' | 'custom';
    value: string;
    enabled: boolean;
    order: number;
    config?: {
        dateFormat?: string;
        counterStart?: number;
        counterPadding?: number;
        caseType?: string;
        replaceFrom?: string;
        replaceTo?: string;
    };
}
export declare enum FileChangeType {
    RENAMED = "renamed",// 文件或文件夹仅名称发生改变，但父目录未变
    MOVED = "moved",// 文件或文件夹被移动到新的目录
    MODIFIED = "modified",// 文件内容发生了改变，但其路径和名称均未改变
    DELETED = "deleted",// 文件或文件夹在工作流处理后被删除
    COPIED = "copied",// 基于一个现有文件创建了一个副本
    CREATED = "created"
}
export interface FileChange {
    type: FileChangeType;
    file: AppFile | null;
    originalFile: AppFile | null;
    stepId: string;
}
export interface WorkflowResult {
    workflowId: string;
    stepResults: StepResult[];
    totalFiles: number;
    processedFiles: number;
    errors: ProcessError[];
    startTime: string;
    endTime: string;
    duration: number;
    changes: FileChange[];
}
export interface StepResult {
    stepId: string;
    stepName: string;
    inputFiles: AppFile[];
    outputFiles: AppFile[];
    processedCount: number;
    errors: ProcessError[];
    duration: number;
}
export interface ProcessError {
    file: string;
    error: string;
    step?: string;
}
export interface HistoryEntry {
    id: string;
    timestamp: string;
    workflowId: string;
    workflowName: string;
    stepId?: string;
    stepName?: string;
    fileOperations: FileOperation[];
    status: 'success' | 'error' | 'partial';
    duration: number;
    totalFiles: number;
    processedFiles: number;
    errors: ProcessError[];
    canUndo?: boolean;
    isUndone?: boolean;
    undoTimestamp?: string;
    createdDirectories?: string[];
    cleanedEmptyDirectories?: string[];
    source?: 'manual' | 'file_watch' | 'scheduled';
    monitorTaskId?: string;
    monitorTaskName?: string;
}
export interface FileOperation {
    id: string;
    originalPath: string;
    originalName: string;
    newPath?: string;
    newName?: string;
    operation: 'move' | 'copy' | 'rename' | 'delete' | 'createFolder';
    status: 'success' | 'error';
    error?: string;
    fileType: string;
    fileSize: number;
    stepId?: string;
    stepName?: string;
}
export interface MonitorTask {
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    type: 'file_watch' | 'scheduled';
    workflowId: string;
    createdAt: string;
    updatedAt: string;
    lastExecuted?: string;
    nextExecution?: string;
    status: 'idle' | 'running' | 'error' | 'disabled';
    config: FileWatchConfig | ScheduledConfig;
    statistics: MonitorStatistics;
}
export interface FileWatchConfig {
    watchPaths: string[];
    ignorePatterns?: string[];
    debounceMs: number;
    events: FileWatchEvent[];
    autoExecute: boolean;
    batchSize?: number;
    batchTimeoutMs?: number;
}
export interface ScheduledConfig {
    cronExpression: string;
    timezone?: string;
    inputPath: string;
    inputPaths?: string[];
    skipIfRunning: boolean;
    ignorePatterns?: string[];
    debounceMs: number;
    events: FileWatchEvent[];
    batchSize?: number;
    batchTimeoutMs?: number;
}
export type FileWatchEvent = 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir';
export interface MonitorStatistics {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    totalFilesProcessed: number;
    lastExecutionDuration?: number;
    averageExecutionTime?: number;
    lastError?: string;
    lastErrorTime?: string;
    errorHistory?: MonitorErrorRecord[];
    consecutiveFailures?: number;
    lastSuccessTime?: string;
}
export interface MonitorErrorRecord {
    timestamp: string;
    error: string;
    errorType: 'workflow_error' | 'file_access_error' | 'system_error' | 'configuration_error';
    context: {
        executionId: string;
        triggeredBy: 'file_change' | 'schedule' | 'manual';
        filesInvolved: string[];
        workflowStep?: string;
        systemInfo?: {
            memoryUsage?: number;
            diskSpace?: number;
        };
    };
    stackTrace?: string;
}
export interface MonitorEvent {
    id: string;
    taskId: string;
    type: 'execution_started' | 'execution_completed' | 'execution_failed' | 'file_detected' | 'task_enabled' | 'task_disabled';
    timestamp: string;
    data: any;
    message?: string;
}
export interface MonitorExecutionResult {
    taskId: string;
    executionId: string;
    startTime: string;
    endTime: string;
    duration: number;
    status: 'success' | 'error' | 'partial';
    filesProcessed: number;
    workflowResult: WorkflowResult;
    triggeredBy: 'file_change' | 'scheduled' | 'manual';
    triggerData?: any;
}
export interface FileSizeRange {
    id: string;
    minSize: number;
    maxSize: number;
    unit: 'B' | 'KB' | 'MB' | 'GB';
    folderName: string;
}
export interface MonitorTaskStatus {
    taskId: string;
    isRunning: boolean;
    currentExecution?: {
        executionId: string;
        startTime: string;
        filesBeingProcessed: number;
    };
    nextScheduledRun?: string;
    lastRun?: {
        timestamp: string;
        status: 'success' | 'error' | 'partial';
        duration: number;
        filesProcessed: number;
    };
}
export interface Rule {
    id: string;
    name: string;
    enabled: boolean;
    conditions: Condition[];
    actions: Action[];
    matchType: 'ALL' | 'ANY';
    order: number;
}
