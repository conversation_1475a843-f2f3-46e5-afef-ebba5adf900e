export interface UndoOperation {
    id: string;
    type: 'move' | 'copy' | 'rename' | 'delete';
    timestamp: number;
    sourcePath: string;
    targetPath: string;
    originalName?: string;
    isDirectory: boolean;
    size?: number;
}
export interface UndoRecord {
    id: string;
    workflowId: string;
    workflowName: string;
    executionId: string;
    timestamp: number;
    operations: UndoOperation[];
    status: 'completed' | 'partial' | 'failed';
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    canUndo: boolean;
    undoReason?: string;
}
export interface UndoProgress {
    total: number;
    completed: number;
    current: string;
    errors: string[];
}
export interface UndoResult {
    success: boolean;
    totalOperations: number;
    successfulOperations: number;
    failedOperations: number;
    errors: string[];
    message: string;
}
export interface UndoConfig {
    maxRecords: number;
    maxAge: number;
    enableAutoCleanup: boolean;
    confirmBeforeUndo: boolean;
}
