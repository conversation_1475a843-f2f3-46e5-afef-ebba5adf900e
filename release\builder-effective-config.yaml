directories:
  output: release
  buildResources: build
appId: com.arroengine.app
productName: ArroEngine
icon: resources/logov1.ico
files:
  - filter:
      - dist/**/*
      - package.json
      - resources/**/*
asarUnpack:
  - node_modules/chokidar/**/*
  - node_modules/fsevents/**/*
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: resources/logov1.ico
  publisherName: ArroEngine
  requestedExecutionLevel: asInvoker
  artifactName: ${productName} ${version} ${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: false
  createDesktopShortcut: always
  createStartMenuShortcut: true
  shortcutName: ArroEngine
  installerIcon: resources/logov1.ico
  uninstallerIcon: resources/logov1.ico
  installerHeaderIcon: resources/logov1.ico
  perMachine: false
  deleteAppDataOnUninstall: false
  include: build/installer.nsh
  artifactName: ${productName} Setup ${version}.${ext}
  displayLanguageSelector: false
  runAfterFinish: true
  menuCategory: false
  warningsAsErrors: false
  differentialPackage: true
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: resources/logov1.ico
  category: public.app-category.productivity
linux:
  target:
    - target: AppImage
      arch:
        - x64
  icon: resources/logov1.ico
  category: Utility
electronVersion: 31.7.7
