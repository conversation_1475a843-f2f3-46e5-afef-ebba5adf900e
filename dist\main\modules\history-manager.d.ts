import type { HistoryEntry, WorkflowResult, Workflow, AppFile } from '../../shared/types';
export declare class HistoryManager {
    private memoryCache;
    private lastCleanupTime;
    private workflowEngine;
    private operationLocks;
    private operationLockTimestamps;
    private isHistoryLocked;
    private historyLockTimestamp;
    private operationLog;
    private recentExecutions;
    private lockCleanupInterval;
    constructor();
    /**
     * 获取操作锁，防止并发操作冲突（增强版，支持超时）
     */
    private acquireOperationLock;
    /**
     * 释放操作锁
     */
    private releaseOperationLock;
    /**
     * 启动锁清理监控
     */
    private startLockCleanupMonitor;
    /**
     * 清理过期的锁
     */
    private cleanupExpiredLocks;
    /**
     * 停止锁清理监控
     */
    private stopLockCleanupMonitor;
    /**
     * 获取历史记录文件锁（增强版，支持超时）
     */
    private acquireHistoryLock;
    /**
     * 释放历史记录文件锁
     */
    private releaseHistoryLock;
    /**
     * 记录操作步骤，用于回滚
     */
    private logOperationStep;
    /**
     * 生成工作流执行的内容哈希，用于重复检测
     */
    private generateExecutionHash;
    /**
     * 检查是否为重复执行
     */
    private isDuplicateExecution;
    /**
     * 回滚操作步骤（增强版，支持部分完成步骤）
     */
    private rollbackOperation;
    /**
     * 回滚单个步骤
     */
    private rollbackSingleStep;
    /**
     * 恢复历史记录条目
     */
    private restoreHistoryEntry;
    /**
     * 验证路径安全性，防止路径遍历攻击
     */
    private validatePathSecurity;
    /**
     * 规范化文件操作路径
     */
    private normalizeOperationPaths;
    /**
     * 批量添加历史记录条目（用于步骤级别记录）
     */
    addEntries(entries: HistoryEntry[]): Promise<void>;
    /**
     * 添加历史记录条目（带重复检测）
     */
    addEntry(entry: HistoryEntry): Promise<void>;
    /**
     * 检查历史记录中是否存在重复条目
     */
    private checkDuplicateEntry;
    /**
     * 判断两个历史记录是否为相同执行
     */
    private isSameExecution;
    /**
     * 生成文件操作的哈希值
     */
    private generateFileOperationsHash;
    /**
     * 执行历史记录清理
     */
    private performCleanup;
    /**
     * 从工作流结果创建历史记录条目
     */
    createEntryFromWorkflowResult(workflowResult: WorkflowResult, workflow: Workflow, originalFiles: AppFile[], source?: 'manual' | 'file_watch' | 'scheduled', monitorTaskId?: string, monitorTaskName?: string, createdDirectories?: string[], cleanedEmptyDirectories?: string[], createStepLevelEntries?: boolean): HistoryEntry | HistoryEntry[];
    /**
     * 获取历史记录（带内存缓存优化）
     */
    getEntries(limit?: number, offset?: number): Promise<HistoryEntry[]>;
    /**
     * 清除内存缓存
     */
    clearMemoryCache(): void;
    /**
     * 评估撤销操作的可行性（增强版）
     */
    private assessUndoFeasibility;
    /**
     * 检测复杂操作序列
     */
    private detectComplexOperationSequence;
    /**
     * 计算重命名链的长度
     */
    private calculateRenameChainLength;
    /**
     * 计算操作时间跨度
     */
    private calculateOperationTimeSpan;
    /**
     * 创建步骤级别的历史记录条目
     */
    private createStepLevelHistoryEntries;
    /**
     * 从步骤结果中提取文件操作
     */
    private extractStepFileOperations;
    /**
     * 检查工作流执行是否可能重复（在执行前调用）
     */
    checkPotentialDuplicate(workflow: Workflow, files: AppFile[]): Promise<{
        isDuplicate: boolean;
        message?: string;
    }>;
    /**
     * 搜索历史记录（使用内存缓存）
     */
    searchEntries(query: string, limit?: number): Promise<HistoryEntry[]>;
    /**
     * 清空历史记录
     */
    clearHistory(): Promise<void>;
    /**
     * 删除单条历史记录
     */
    deleteEntry(entryId: string): Promise<boolean>;
    /**
     * 检查文件/文件夹权限（复用工作流引擎的权限检查）
     */
    private checkPermissions;
    /**
     * 检查目录权限（包括父目录）
     */
    private checkDirectoryPermissions;
    /**
     * 检查磁盘空间是否足够（复用工作流引擎的磁盘空间检查）
     */
    private checkDiskSpace;
    /**
     * 计算撤回操作所需的磁盘空间
     */
    private calculateRequiredSpace;
    /**
     * 分类和格式化错误信息（复用工作流引擎的错误分类）
     */
    private categorizeError;
    /**
     * 生成用户友好的错误建议（增强版）
     */
    private generateErrorSuggestion;
    /**
     * 创建结构化错误报告
     */
    private createErrorReport;
    /**
     * 事务性更新历史记录条目状态
     */
    private updateHistoryEntryStatus;
    /**
     * 手动触发清理
     */
    manualCleanup(): Promise<{
        deletedCount: number;
        message: string;
    }>;
    /**
     * 撤销历史记录操作
     */
    undoEntry(entryId: string): Promise<{
        success: boolean;
        message?: string;
        requiresChainUndo?: boolean;
        entryId?: string;
    }>;
    /**
     * 执行撤销操作的核心逻辑
     */
    private performUndoEntry;
    /**
     * 连锁撤回操作 - 处理连锁重命名冲突
     */
    chainUndoEntry(entryId: string): Promise<{
        success: boolean;
        message?: string;
    }>;
    /**
     * 重做已撤销的操作
     */
    redoEntry(entryId: string): Promise<{
        success: boolean;
        message?: string;
    }>;
    /**
     * 执行连锁撤回操作
     */
    private performChainUndoOperations;
    /**
     * 执行撤销操作
     */
    private performUndoOperations;
    /**
     * 撤销移动或重命名操作 (终极修复版：采用“偏执”的两步法)
     */
    private undoMoveOrRename;
    /**
     * 撤销创建文件夹操作
     */
    private undoCreateFolder;
    /**
     * 撤销复制操作
     */
    private undoCopy;
    /**
     * 预检查重做操作的可行性
     */
    private preCheckRedoOperations;
    /**
     * 预检查撤销操作的可行性 (支持连锁重命名检测)
     */
    private preCheckUndoOperations;
    /**
     * 检测连锁重命名冲突
     */
    private detectChainRenameConflicts;
    /**
     * 分析连锁依赖关系
     */
    private analyzeChainDependencies;
    /**
     * 计算撤回执行顺序
     */
    private calculateUndoOrder;
    /**
     * 执行重做操作 (终极修复版：采用“偏执”的两步法)
     */
    private performRedoOperations;
    /**
     * 清理工作流执行过程中创建的文件夹
     */
    private cleanupCreatedDirectories;
    /**
     * 恢复被清理的空文件夹
     */
    private restoreCleanedEmptyDirectories;
    /**
     * 清理部分完成的步骤
     */
    private cleanupPartialStep;
    /**
     * 中止正在进行的步骤
     */
    private abortInProgressStep;
    /**
     * 清理未开始的步骤
     */
    private cleanupUnstartedStep;
    /**
     * 创建操作进度跟踪器
     */
    private createProgressTracker;
    /**
     * 增强的用户反馈机制
     */
    private generateUserFeedback;
}
