import fs from 'fs-extra';
import path from 'path';
import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import type { HistoryEntry, FileOperation, WorkflowResult, Workflow, AppFile, StepResult } from '../../shared/types';
import { WorkflowEngine } from './workflow-engine';

// 历史记录文件路径
const HISTORY_FILE_PATH = path.join(app.getPath('userData'), 'history.json');

// 历史记录配置
const HISTORY_CONFIG = {
  MAX_ENTRIES: 1000,           // 最大历史记录数量
  AUTO_CLEANUP_DAYS: 30,       // 自动清理超过30天的记录
  MEMORY_CLEANUP_THRESHOLD: 500, // 内存中超过500条时触发清理
  DUPLICATE_CHECK_WINDOW: 5000,  // 重复检测时间窗口（毫秒）
  MIN_EXECUTION_INTERVAL: 1000   // 最小执行间隔（毫秒）
};

/**
 * 操作步骤接口，用于记录和回滚（增强版）
 */
interface OperationStep {
  id: string;
  type: 'file_move' | 'file_copy' | 'file_delete' | 'folder_create' | 'folder_delete' | 'history_update';
  sourcePath?: string;
  targetPath?: string;
  backupPath?: string; // 备份路径，用于回滚
  metadata?: any;
  timestamp: number;
  completed: boolean;
  // 新增状态字段
  partiallyCompleted?: boolean; // 部分完成
  inProgress?: boolean; // 正在进行中
  tempFiles?: string[]; // 临时文件列表，用于清理
  rollbackData?: any; // 回滚所需的额外数据
}

// 读取历史记录
async function loadHistory(): Promise<HistoryEntry[]> {
  try {
    if (await fs.pathExists(HISTORY_FILE_PATH)) {
      const data = await fs.readFile(HISTORY_FILE_PATH, 'utf-8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Failed to load history:', error);
    return [];
  }
}

// 保存历史记录
async function saveHistory(history: HistoryEntry[]): Promise<void> {
  try {
    await fs.ensureDir(path.dirname(HISTORY_FILE_PATH));
    await fs.writeFile(HISTORY_FILE_PATH, JSON.stringify(history, null, 2), 'utf-8');
  } catch (error) {
    console.error('Failed to save history:', error);
    throw error;
  }
}

export class HistoryManager {
  private memoryCache: HistoryEntry[] | null = null;
  private lastCleanupTime: number = 0;
  private workflowEngine: WorkflowEngine;
  private operationLocks: Map<string, Promise<any>> = new Map(); // 操作锁
  private operationLockTimestamps: Map<string, number> = new Map(); // 操作锁时间戳
  private isHistoryLocked: boolean = false; // 历史记录文件锁
  private historyLockTimestamp: number = 0; // 历史记录锁时间戳
  private operationLog: Map<string, OperationStep[]> = new Map(); // 操作日志，用于回滚
  private recentExecutions: Map<string, number> = new Map(); // 最近执行记录，用于重复检测
  private lockCleanupInterval: NodeJS.Timeout | null = null; // 锁清理定时器

  constructor() {
    this.workflowEngine = new WorkflowEngine();
    this.startLockCleanupMonitor();
  }

  /**
   * 获取操作锁，防止并发操作冲突（增强版，支持超时）
   */
  private async acquireOperationLock(entryId: string, timeout: number = 60000): Promise<void> {
    const existingLock = this.operationLocks.get(entryId);
    if (existingLock) {
      console.log(`[并发控制] 等待操作锁释放: ${entryId}`);

      // 使用Promise.race实现超时控制
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`等待操作锁超时 (${timeout}ms): ${entryId}`));
        }, timeout);
      });

      try {
        await Promise.race([existingLock, timeoutPromise]);
        console.log(`[并发控制] 操作锁已释放: ${entryId}`);
      } catch (error) {
        // 如果是超时错误，强制清理锁
        if (error instanceof Error && error.message.includes('超时')) {
          console.warn(`[并发控制] 强制清理超时的操作锁: ${entryId}`);
          this.operationLocks.delete(entryId);
          throw error;
        }
        // 其他错误继续抛出
        throw error;
      }
    }
  }

  /**
   * 释放操作锁
   */
  private releaseOperationLock(entryId: string): void {
    this.operationLocks.delete(entryId);
    this.operationLockTimestamps.delete(entryId);
    console.log(`[并发控制] 操作锁已释放: ${entryId}`);
  }

  /**
   * 启动锁清理监控
   */
  private startLockCleanupMonitor(): void {
    // 每5分钟检查一次过期的锁
    this.lockCleanupInterval = setInterval(() => {
      this.cleanupExpiredLocks();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期的锁
   */
  private cleanupExpiredLocks(): void {
    const now = Date.now();
    const lockTimeout = 10 * 60 * 1000; // 10分钟超时

    // 清理过期的操作锁
    for (const [entryId, timestamp] of this.operationLockTimestamps) {
      if (now - timestamp > lockTimeout) {
        console.warn(`[并发控制] 清理过期的操作锁: ${entryId}, 持续时间: ${now - timestamp}ms`);
        this.operationLocks.delete(entryId);
        this.operationLockTimestamps.delete(entryId);
      }
    }

    // 清理过期的历史记录锁
    if (this.isHistoryLocked && this.historyLockTimestamp > 0) {
      if (now - this.historyLockTimestamp > lockTimeout) {
        console.warn(`[并发控制] 清理过期的历史记录锁, 持续时间: ${now - this.historyLockTimestamp}ms`);
        this.isHistoryLocked = false;
        this.historyLockTimestamp = 0;
      }
    }
  }

  /**
   * 停止锁清理监控
   */
  private stopLockCleanupMonitor(): void {
    if (this.lockCleanupInterval) {
      clearInterval(this.lockCleanupInterval);
      this.lockCleanupInterval = null;
    }
  }

  /**
   * 获取历史记录文件锁（增强版，支持超时）
   */
  private async acquireHistoryLock(timeout: number = 30000): Promise<void> {
    const startTime = Date.now();
    let retryCount = 0;
    const maxRetries = Math.floor(timeout / 50); // 每50ms重试一次

    while (this.isHistoryLocked) {
      if (Date.now() - startTime > timeout) {
        throw new Error(`获取历史记录锁超时 (${timeout}ms)，可能存在死锁。重试次数: ${retryCount}`);
      }

      retryCount++;

      // 指数退避策略，避免过度竞争
      const backoffDelay = Math.min(50 * Math.pow(1.1, retryCount / 10), 200);
      await new Promise(resolve => setTimeout(resolve, backoffDelay));

      // 每100次重试输出警告
      if (retryCount % 100 === 0) {
        console.warn(`[并发控制] 历史记录锁等待时间过长，已重试 ${retryCount} 次`);
      }
    }

    this.isHistoryLocked = true;
    this.historyLockTimestamp = Date.now();

    if (retryCount > 0) {
      console.log(`[并发控制] 成功获取历史记录锁，重试次数: ${retryCount}, 等待时间: ${Date.now() - startTime}ms`);
    }
  }

  /**
   * 释放历史记录文件锁
   */
  private releaseHistoryLock(): void {
    this.isHistoryLocked = false;
    this.historyLockTimestamp = 0;
  }

  /**
   * 记录操作步骤，用于回滚
   */
  private logOperationStep(operationId: string, step: OperationStep): void {
    if (!this.operationLog.has(operationId)) {
      this.operationLog.set(operationId, []);
    }
    this.operationLog.get(operationId)!.push(step);
  }

  /**
   * 生成工作流执行的内容哈希，用于重复检测
   */
  private generateExecutionHash(workflowResult: WorkflowResult, workflow: Workflow, originalFiles: AppFile[]): string {
    const content = {
      workflowId: workflow.id,
      workflowName: workflow.name,
      fileCount: originalFiles.length,
      filePaths: originalFiles.map(f => f.path).sort(), // 排序确保一致性
      stepCount: workflowResult.stepResults.length,
      stepIds: workflowResult.stepResults.map(s => s.stepId).sort()
    };

    return crypto.createHash('md5').update(JSON.stringify(content)).digest('hex');
  }

  /**
   * 检查是否为重复执行
   */
  private isDuplicateExecution(executionHash: string, timestamp: string): boolean {
    const now = Date.now();
    const executionTime = new Date(timestamp).getTime();

    // 清理过期的执行记录
    for (const [hash, time] of this.recentExecutions.entries()) {
      if (now - time > HISTORY_CONFIG.DUPLICATE_CHECK_WINDOW) {
        this.recentExecutions.delete(hash);
      }
    }

    // 检查是否存在重复
    const lastExecution = this.recentExecutions.get(executionHash);
    if (lastExecution && (executionTime - lastExecution) < HISTORY_CONFIG.MIN_EXECUTION_INTERVAL) {
      console.warn(`[重复检测] 检测到重复执行，哈希: ${executionHash}, 间隔: ${executionTime - lastExecution}ms`);
      return true;
    }

    // 记录本次执行
    this.recentExecutions.set(executionHash, executionTime);
    return false;
  }

  /**
   * 回滚操作步骤（增强版，支持部分完成步骤）
   */
  private async rollbackOperation(operationId: string): Promise<void> {
    const steps = this.operationLog.get(operationId);
    if (!steps) return;

    console.log(`🔄 开始回滚操作 ${operationId}，共 ${steps.length} 个步骤`);

    let rollbackSuccessCount = 0;
    let rollbackFailureCount = 0;
    const rollbackErrors: string[] = [];

    // 按相反顺序回滚步骤
    for (let i = steps.length - 1; i >= 0; i--) {
      const step = steps[i];

      try {
        if (step.completed) {
          // 回滚已完成的步骤
          await this.rollbackSingleStep(step);
          console.log(`✅ 回滚已完成步骤: ${step.type} - ${step.id}`);
          rollbackSuccessCount++;
        } else if (step.partiallyCompleted) {
          // 处理部分完成的步骤
          await this.cleanupPartialStep(step);
          console.log(`🧹 清理部分完成步骤: ${step.type} - ${step.id}`);
          rollbackSuccessCount++;
        } else if (step.inProgress) {
          // 处理正在进行的步骤
          await this.abortInProgressStep(step);
          console.log(`⏹️ 中止进行中步骤: ${step.type} - ${step.id}`);
          rollbackSuccessCount++;
        } else {
          // 未开始的步骤，只需要清理资源
          await this.cleanupUnstartedStep(step);
          console.log(`🗑️ 清理未开始步骤: ${step.type} - ${step.id}`);
          rollbackSuccessCount++;
        }
      } catch (error) {
        const errorMsg = `回滚步骤失败: ${step.type} - ${step.id}: ${error instanceof Error ? error.message : String(error)}`;
        console.error(`❌ ${errorMsg}`, error);
        rollbackErrors.push(errorMsg);
        rollbackFailureCount++;
        // 继续回滚其他步骤，不要因为一个失败就停止
      }
    }

    // 记录回滚结果
    console.log(`🔄 回滚操作完成: ${operationId}`);
    console.log(`   ✅ 成功: ${rollbackSuccessCount} 个步骤`);
    console.log(`   ❌ 失败: ${rollbackFailureCount} 个步骤`);

    if (rollbackErrors.length > 0) {
      console.warn(`⚠️ 回滚过程中的错误:`, rollbackErrors);
    }

    // 清理操作日志
    this.operationLog.delete(operationId);
  }

  /**
   * 回滚单个步骤
   */
  private async rollbackSingleStep(step: OperationStep): Promise<void> {
    switch (step.type) {
      case 'file_move':
        if (step.targetPath && step.sourcePath && await fs.pathExists(step.targetPath)) {
          await fs.move(step.targetPath, step.sourcePath);
        }
        break;
      case 'file_copy':
        if (step.targetPath && await fs.pathExists(step.targetPath)) {
          await fs.remove(step.targetPath);
        }
        break;
      case 'file_delete':
        if (step.backupPath && step.sourcePath && await fs.pathExists(step.backupPath)) {
          await fs.move(step.backupPath, step.sourcePath);
        }
        break;
      case 'folder_create':
        if (step.targetPath && await fs.pathExists(step.targetPath)) {
          const items = await fs.readdir(step.targetPath);
          if (items.length === 0) {
            await fs.rmdir(step.targetPath);
          }
        }
        break;
      case 'history_update':
        // 历史记录的回滚需要特殊处理
        if (step.metadata && step.metadata.originalEntry) {
          await this.restoreHistoryEntry(step.metadata.originalEntry);
        }
        break;
    }
  }

  /**
   * 恢复历史记录条目
   */
  private async restoreHistoryEntry(originalEntry: HistoryEntry): Promise<void> {
    await this.acquireHistoryLock();
    try {
      const history = await loadHistory();
      const entryIndex = history.findIndex(entry => entry.id === originalEntry.id);
      if (entryIndex !== -1) {
        history[entryIndex] = originalEntry;
        await saveHistory(history);
        this.clearMemoryCache();
      }
    } finally {
      this.releaseHistoryLock();
    }
  }

  /**
   * 验证路径安全性，防止路径遍历攻击
   */
  private validatePathSecurity(filePath: string, basePath?: string): { isValid: boolean; error?: string } {
    try {
      // 规范化路径
      const normalizedPath = require('path').resolve(filePath);

      // 检查路径是否包含危险字符
      if (filePath.includes('..') || filePath.includes('~')) {
        return { isValid: false, error: '路径包含不安全字符' };
      }

      // 如果提供了基础路径，检查是否在允许范围内
      if (basePath) {
        const normalizedBasePath = require('path').resolve(basePath);
        if (!normalizedPath.startsWith(normalizedBasePath)) {
          return { isValid: false, error: '路径超出允许范围' };
        }
      }

      // 检查路径长度（Windows 路径限制）
      if (normalizedPath.length > 260) {
        return { isValid: false, error: '路径过长' };
      }

      // 检查是否为系统关键目录
      const systemPaths = [
        'C:\\Windows',
        'C:\\Program Files',
        'C:\\Program Files (x86)',
        '/System',
        '/usr/bin',
        '/bin',
        '/sbin'
      ];

      for (const systemPath of systemPaths) {
        if (normalizedPath.toLowerCase().startsWith(systemPath.toLowerCase())) {
          return { isValid: false, error: '不能操作系统关键目录' };
        }
      }

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: `路径验证失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * 规范化文件操作路径
   */
  private normalizeOperationPaths(operation: FileOperation): { isValid: boolean; error?: string; normalizedOperation?: FileOperation } {
    try {
      const normalizedOperation = { ...operation };

      // 验证原始路径
      if (operation.originalPath) {
        const validation = this.validatePathSecurity(operation.originalPath);
        if (!validation.isValid) {
          return { isValid: false, error: `原始路径不安全: ${validation.error}` };
        }
        normalizedOperation.originalPath = require('path').resolve(operation.originalPath);
      }

      // 验证新路径
      if (operation.newPath) {
        const validation = this.validatePathSecurity(operation.newPath);
        if (!validation.isValid) {
          return { isValid: false, error: `目标路径不安全: ${validation.error}` };
        }
        normalizedOperation.newPath = require('path').resolve(operation.newPath);
      }

      return { isValid: true, normalizedOperation };
    } catch (error) {
      return { isValid: false, error: `路径规范化失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * 批量添加历史记录条目（用于步骤级别记录）
   */
  async addEntries(entries: HistoryEntry[]): Promise<void> {
    if (entries.length === 0) return;

    await this.acquireHistoryLock();
    try {
      const history = await loadHistory();

      // 逐个检查重复并添加
      for (const entry of entries) {
        const isDuplicate = this.checkDuplicateEntry(entry, history);
        if (!isDuplicate) {
          history.unshift(entry);
        } else {
          console.warn(`[重复检测] 跳过重复的步骤历史记录: ${entry.stepName} (${entry.timestamp})`);
        }
      }

      // 执行清理策略
      const cleanedHistory = this.performCleanup(history);

      await saveHistory(cleanedHistory);

      // 清除内存缓存，强制下次重新加载
      this.memoryCache = null;
    } finally {
      this.releaseHistoryLock();
    }
  }

  /**
   * 添加历史记录条目（带重复检测）
   */
  async addEntry(entry: HistoryEntry): Promise<void> {
    await this.acquireHistoryLock();
    try {
      const history = await loadHistory();

      // 检查是否为重复记录
      const isDuplicate = this.checkDuplicateEntry(entry, history);
      if (isDuplicate) {
        console.warn(`[重复检测] 跳过重复的历史记录: ${entry.workflowName} (${entry.timestamp})`);
        return;
      }

      history.unshift(entry); // 添加到开头，最新的在前面

      // 执行清理策略
      const cleanedHistory = this.performCleanup(history);

      await saveHistory(cleanedHistory);

      // 清除内存缓存，强制下次重新加载
      this.memoryCache = null;
    } finally {
      this.releaseHistoryLock();
    }
  }

  /**
   * 检查历史记录中是否存在重复条目
   */
  private checkDuplicateEntry(newEntry: HistoryEntry, existingHistory: HistoryEntry[]): boolean {
    const newTime = new Date(newEntry.timestamp).getTime();

    // 只检查最近的记录，提高性能
    const recentEntries = existingHistory.slice(0, 50);

    for (const existingEntry of recentEntries) {
      const existingTime = new Date(existingEntry.timestamp).getTime();

      // 如果时间差超过检测窗口，停止检查
      if (newTime - existingTime > HISTORY_CONFIG.DUPLICATE_CHECK_WINDOW) {
        break;
      }

      // 检查是否为重复记录
      if (this.isSameExecution(newEntry, existingEntry)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 判断两个历史记录是否为相同执行
   */
  private isSameExecution(entry1: HistoryEntry, entry2: HistoryEntry): boolean {
    // 基本信息匹配
    if (entry1.workflowId !== entry2.workflowId ||
        entry1.totalFiles !== entry2.totalFiles ||
        entry1.processedFiles !== entry2.processedFiles) {
      return false;
    }

    // 时间间隔检查
    const time1 = new Date(entry1.timestamp).getTime();
    const time2 = new Date(entry2.timestamp).getTime();
    if (Math.abs(time1 - time2) > HISTORY_CONFIG.MIN_EXECUTION_INTERVAL) {
      return false;
    }

    // 文件操作匹配检查
    if (entry1.fileOperations.length !== entry2.fileOperations.length) {
      return false;
    }

    // 生成文件操作的哈希进行比较
    const hash1 = this.generateFileOperationsHash(entry1.fileOperations);
    const hash2 = this.generateFileOperationsHash(entry2.fileOperations);

    return hash1 === hash2;
  }

  /**
   * 生成文件操作的哈希值
   */
  private generateFileOperationsHash(operations: FileOperation[]): string {
    const content = operations.map(op => ({
      operation: op.operation,
      originalPath: op.originalPath,
      newPath: op.newPath,
      status: op.status
    })).sort((a, b) => (a.originalPath || '').localeCompare(b.originalPath || ''));

    return crypto.createHash('md5').update(JSON.stringify(content)).digest('hex');
  }

  /**
   * 执行历史记录清理
   */
  private performCleanup(history: HistoryEntry[]): HistoryEntry[] {
    const now = Date.now();

    // 1. 按数量限制清理
    if (history.length > HISTORY_CONFIG.MAX_ENTRIES) {
      history = history.slice(0, HISTORY_CONFIG.MAX_ENTRIES);
      console.log(`[历史记录] 按数量限制清理，保留最近${HISTORY_CONFIG.MAX_ENTRIES}条记录`);
    }

    // 2. 按时间清理（每小时最多执行一次）
    if (now - this.lastCleanupTime > 60 * 60 * 1000) {
      const cutoffTime = now - (HISTORY_CONFIG.AUTO_CLEANUP_DAYS * 24 * 60 * 60 * 1000);
      const beforeCount = history.length;

      history = history.filter(entry => {
        const entryTime = new Date(entry.timestamp).getTime();
        return entryTime > cutoffTime;
      });

      if (beforeCount !== history.length) {
        console.log(`[历史记录] 按时间清理，删除了${beforeCount - history.length}条超过${HISTORY_CONFIG.AUTO_CLEANUP_DAYS}天的记录`);
      }

      this.lastCleanupTime = now;
    }

    return history;
  }

  /**
   * 从工作流结果创建历史记录条目
   */
  createEntryFromWorkflowResult(
    workflowResult: WorkflowResult,
    workflow: Workflow,
    originalFiles: AppFile[],
    source: 'manual' | 'file_watch' | 'scheduled' = 'manual',
    monitorTaskId?: string,
    monitorTaskName?: string,
    createdDirectories?: string[],
    cleanedEmptyDirectories?: string[],
    createStepLevelEntries: boolean = false
  ): HistoryEntry | HistoryEntry[] {
    // 如果需要创建步骤级别的历史记录
    if (createStepLevelEntries && workflowResult.stepResults.length > 1) {
      return this.createStepLevelHistoryEntries(
        workflowResult,
        workflow,
        originalFiles,
        source,
        monitorTaskId,
        monitorTaskName,
        createdDirectories,
        cleanedEmptyDirectories
      );
    }

    // 创建工作流级别的历史记录（原有逻辑）
    const fileOperations: FileOperation[] = [];

    // 遍历每个步骤的结果来构建文件操作记录
    for (const stepResult of workflowResult.stepResults) {
      // 找到对应的工作流步骤
      const workflowStep = workflow.steps.find(step => step.id === stepResult.stepId);

      for (let i = 0; i < stepResult.outputFiles.length; i++) {
        const outputFile = stepResult.outputFiles[i];
        const inputFile = stepResult.inputFiles[i];

        if (inputFile && outputFile) {
          // 确定操作类型 - 优先使用工作流步骤中的动作类型
          let operation: 'move' | 'copy' | 'rename' | 'delete' = 'move';

          if (outputFile.path === '将被删除') {
            operation = 'delete';
          } else if (inputFile.path !== outputFile.path) {
            // 如果找到了对应的工作流步骤，使用步骤中的动作类型
            if (workflowStep && workflowStep.actions.length > 0) {
              // 获取第一个启用的动作类型
              const firstEnabledAction = workflowStep.actions.find(action => action.enabled);
              if (firstEnabledAction) {
                operation = firstEnabledAction.type as 'move' | 'copy' | 'rename' | 'delete';
              }
            } else {
              // 回退到基于路径的推断
              const inputDir = require('path').dirname(inputFile.path);
              const outputDir = require('path').dirname(outputFile.path);

              if (inputDir === outputDir) {
                operation = 'rename';
              } else {
                operation = 'move';
              }
            }
          }

          // 对于所有操作，originalPath都应该是完整的原始路径
          // 撤销时会将文件/文件夹移回到这个完整路径
          let originalPath = inputFile.path;
          console.log(`📝 操作记录: ${inputFile.path} -> ${outputFile.path} (${operation})`);
          console.log(`📝 撤销时将回到: ${originalPath}`);

          const fileOperation: FileOperation = {
            id: `op-${uuidv4()}`,
            originalPath: originalPath,
            originalName: inputFile.name,
            newPath: outputFile.path !== '将被删除' ? outputFile.path : undefined,
            newName: outputFile.path !== '将被删除' ? outputFile.name : undefined,
            operation,
            status: outputFile.status === 'error' ? 'error' : 'success',
            error: outputFile.error,
            fileType: inputFile.type || 'unknown',
            fileSize: inputFile.size || 0
          };
          
          fileOperations.push(fileOperation);
        }
      }
    }
    
    // 添加被清理的空文件夹到文件操作记录中
    const allFileOperations = [...fileOperations];
    if (cleanedEmptyDirectories && cleanedEmptyDirectories.length > 0) {
      const emptyFolderOperations: FileOperation[] = cleanedEmptyDirectories.map(dirPath => ({
        id: `empty-folder-${uuidv4()}`,
        originalPath: dirPath,
        originalName: path.basename(dirPath),
        newPath: undefined, // 被清理，没有新路径
        newName: undefined,
        operation: 'delete' as const, // 使用标准操作类型
        status: 'success' as const,
        fileType: 'folder',
        fileSize: 0,
        error: undefined
      }));
      allFileOperations.push(...emptyFolderOperations);
    }

    // 检查是否包含删除操作
    const hasDeleteOperation = fileOperations.some(op => op.operation === 'delete');

    // 检查是否有成功的文件操作
    const hasSuccessfulOperations = fileOperations.some(op => op.status === 'success');

    // 增强的撤销可行性检查
    const undoFeasibility = this.assessUndoFeasibility(fileOperations, workflowResult);

    // 创建历史记录条目
    const historyEntry: HistoryEntry = {
      id: `history-${uuidv4()}`,
      timestamp: workflowResult.startTime,
      workflowId: workflow.id,
      workflowName: workflow.name,
      fileOperations: allFileOperations,
      status: workflowResult.errors.length === 0 ? 'success' :
              workflowResult.errors.length === workflowResult.totalFiles ? 'error' : 'partial',
      duration: workflowResult.duration,
      totalFiles: workflowResult.totalFiles,
      processedFiles: workflowResult.processedFiles,
      errors: workflowResult.errors,
      // 增强的撤销判断逻辑
      canUndo: undoFeasibility.canUndo,
      isUndone: false,
      // 工作流执行过程中创建的文件夹
      createdDirectories: createdDirectories || [],
      // 被清理的空文件夹（用于撤销时恢复）
      cleanedEmptyDirectories: cleanedEmptyDirectories || [],
      // 监控来源相关字段
      source,
      monitorTaskId,
      monitorTaskName
    };
    
    return historyEntry;
  }

  /**
   * 获取历史记录（带内存缓存优化）
   */
  async getEntries(limit?: number, offset?: number): Promise<HistoryEntry[]> {
    // 使用内存缓存减少文件读取
    if (!this.memoryCache) {
      this.memoryCache = await loadHistory();

      // 如果内存中的记录过多，触发清理
      if (this.memoryCache.length > HISTORY_CONFIG.MEMORY_CLEANUP_THRESHOLD) {
        this.memoryCache = this.performCleanup(this.memoryCache);
        await saveHistory(this.memoryCache);
      }
    }

    if (limit !== undefined && offset !== undefined) {
      return this.memoryCache.slice(offset, offset + limit);
    }

    return this.memoryCache;
  }

  /**
   * 清除内存缓存
   */
  clearMemoryCache(): void {
    this.memoryCache = null;
  }

  /**
   * 评估撤销操作的可行性（增强版）
   */
  private assessUndoFeasibility(
    fileOperations: FileOperation[],
    workflowResult: WorkflowResult
  ): { canUndo: boolean; reason?: string } {
    // 基本检查：是否有成功的操作
    const successfulOperations = fileOperations.filter(op => op.status === 'success');
    if (successfulOperations.length === 0) {
      return { canUndo: false, reason: '没有成功的操作可以撤销' };
    }

    // 检查是否包含删除操作
    const hasDeleteOperation = fileOperations.some(op => op.operation === 'delete');
    if (hasDeleteOperation) {
      return { canUndo: false, reason: '包含删除操作，无法完全撤销' };
    }

    // 检查是否有系统级错误
    const hasSystemErrors = workflowResult.errors.some(error =>
      error.error.includes('权限') ||
      error.error.includes('磁盘空间') ||
      error.error.includes('系统') ||
      error.error.includes('EACCES') ||
      error.error.includes('ENOSPC')
    );
    if (hasSystemErrors) {
      return { canUndo: false, reason: '存在系统级错误，撤销可能不安全' };
    }

    // 检查部分失败的风险
    const failedOperations = fileOperations.filter(op => op.status === 'error');
    const successRate = successfulOperations.length / fileOperations.length;

    // 如果成功率低于50%，认为风险较高
    if (successRate < 0.5 && failedOperations.length > 0) {
      return { canUndo: false, reason: '成功率过低，撤销可能导致数据不一致' };
    }

    // 检查是否有复杂的操作序列（如连锁重命名）
    const hasComplexOperations = this.detectComplexOperationSequence(successfulOperations);
    if (hasComplexOperations.isComplex && hasComplexOperations.riskLevel === 'high') {
      return { canUndo: false, reason: '包含复杂操作序列，建议使用连锁撤销' };
    }

    // 检查文件操作的时间跨度
    const operationTimeSpan = this.calculateOperationTimeSpan(workflowResult);
    if (operationTimeSpan > 3600000) { // 超过1小时
      return { canUndo: false, reason: '操作时间跨度过长，文件状态可能已发生变化' };
    }

    return { canUndo: true };
  }

  /**
   * 检测复杂操作序列
   */
  private detectComplexOperationSequence(operations: FileOperation[]): { isComplex: boolean; riskLevel: 'low' | 'medium' | 'high' } {
    // 检查是否有循环依赖
    const pathMap = new Map<string, string>();
    let hasCircularDependency = false;

    for (const op of operations) {
      if (op.operation === 'move' || op.operation === 'rename') {
        if (op.originalPath && op.newPath) {
          // 检查是否形成循环
          if (pathMap.has(op.newPath) && pathMap.get(op.newPath) === op.originalPath) {
            hasCircularDependency = true;
            break;
          }
          pathMap.set(op.originalPath, op.newPath);
        }
      }
    }

    // 检查重命名链的长度
    const renameChainLength = this.calculateRenameChainLength(operations);

    if (hasCircularDependency || renameChainLength > 5) {
      return { isComplex: true, riskLevel: 'high' };
    } else if (renameChainLength > 2) {
      return { isComplex: true, riskLevel: 'medium' };
    }

    return { isComplex: false, riskLevel: 'low' };
  }

  /**
   * 计算重命名链的长度
   */
  private calculateRenameChainLength(operations: FileOperation[]): number {
    const dependencies = new Map<string, string>();

    for (const op of operations) {
      if ((op.operation === 'move' || op.operation === 'rename') && op.originalPath && op.newPath) {
        dependencies.set(op.originalPath, op.newPath);
      }
    }

    let maxChainLength = 0;
    const visited = new Set<string>();

    for (const [start] of dependencies) {
      if (visited.has(start)) continue;

      let current = start;
      let chainLength = 0;
      const chainVisited = new Set<string>();

      while (dependencies.has(current) && !chainVisited.has(current)) {
        chainVisited.add(current);
        visited.add(current);
        current = dependencies.get(current)!;
        chainLength++;
      }

      maxChainLength = Math.max(maxChainLength, chainLength);
    }

    return maxChainLength;
  }

  /**
   * 计算操作时间跨度
   */
  private calculateOperationTimeSpan(workflowResult: WorkflowResult): number {
    const startTime = new Date(workflowResult.startTime).getTime();
    const endTime = new Date(workflowResult.endTime).getTime();
    return endTime - startTime;
  }

  /**
   * 创建步骤级别的历史记录条目
   */
  private createStepLevelHistoryEntries(
    workflowResult: WorkflowResult,
    workflow: Workflow,
    originalFiles: AppFile[],
    source: 'manual' | 'file_watch' | 'scheduled' = 'manual',
    monitorTaskId?: string,
    monitorTaskName?: string,
    createdDirectories?: string[],
    cleanedEmptyDirectories?: string[]
  ): HistoryEntry[] {
    const entries: HistoryEntry[] = [];

    // 为每个有实际操作的步骤创建独立的历史记录
    for (const stepResult of workflowResult.stepResults) {
      // 只为有文件操作的步骤创建记录
      const stepFileOperations = this.extractStepFileOperations(stepResult, workflow);
      if (stepFileOperations.length === 0) {
        continue;
      }

      // 检查是否包含删除操作
      const hasDeleteOperation = stepFileOperations.some(op => op.operation === 'delete');
      // 检查是否有成功的文件操作
      const hasSuccessfulOperations = stepFileOperations.some(op => op.status === 'success');

      // 计算步骤状态
      const stepStatus = stepResult.errors.length === 0 ? 'success' :
                        stepResult.errors.length === stepFileOperations.length ? 'error' : 'partial';

      const stepEntry: HistoryEntry = {
        id: `history-step-${uuidv4()}`,
        timestamp: workflowResult.startTime,
        workflowId: workflow.id,
        workflowName: workflow.name,
        stepId: stepResult.stepId,
        stepName: stepResult.stepName,
        fileOperations: stepFileOperations,
        status: stepStatus,
        duration: stepResult.duration,
        totalFiles: stepResult.inputFiles.length,
        processedFiles: stepResult.processedCount,
        errors: stepResult.errors,
        canUndo: hasSuccessfulOperations && !hasDeleteOperation,
        isUndone: false,
        createdDirectories: createdDirectories || [],
        cleanedEmptyDirectories: cleanedEmptyDirectories || [],
        source,
        monitorTaskId,
        monitorTaskName
      };

      entries.push(stepEntry);
    }

    return entries;
  }

  /**
   * 从步骤结果中提取文件操作
   */
  private extractStepFileOperations(stepResult: StepResult, workflow: Workflow): FileOperation[] {
    const fileOperations: FileOperation[] = [];

    // 找到对应的工作流步骤
    const workflowStep = workflow.steps.find(step => step.id === stepResult.stepId);

    for (let i = 0; i < stepResult.outputFiles.length; i++) {
      const outputFile = stepResult.outputFiles[i];
      const inputFile = stepResult.inputFiles[i];

      if (inputFile && outputFile && inputFile.path !== outputFile.path) {
        // 确定操作类型 - 优先使用工作流步骤中的动作类型
        let operation: 'move' | 'copy' | 'rename' | 'delete' = 'move';

        if (outputFile.path === '将被删除') {
          operation = 'delete';
        } else if (inputFile.path !== outputFile.path) {
          // 如果找到了对应的工作流步骤，使用步骤中的动作类型
          if (workflowStep && workflowStep.actions.length > 0) {
            // 获取第一个启用的动作类型
            const firstEnabledAction = workflowStep.actions.find(action => action.enabled);
            if (firstEnabledAction) {
              operation = firstEnabledAction.type as 'move' | 'copy' | 'rename' | 'delete';
            }
          } else {
            // 回退到基于路径的推断
            const inputDir = require('path').dirname(inputFile.path);
            const outputDir = require('path').dirname(outputFile.path);

            if (inputDir === outputDir) {
              operation = 'rename';
            } else {
              operation = 'move';
            }
          }
        }

        const fileOperation: FileOperation = {
          id: `op-step-${uuidv4()}`,
          originalPath: inputFile.path,
          originalName: inputFile.name,
          newPath: outputFile.path !== '将被删除' ? outputFile.path : undefined,
          newName: outputFile.path !== '将被删除' ? outputFile.name : undefined,
          operation,
          status: outputFile.status === 'error' ? 'error' : 'success',
          error: outputFile.error,
          fileType: inputFile.type || 'unknown',
          fileSize: inputFile.size || 0,
          stepId: stepResult.stepId,
          stepName: stepResult.stepName
        };

        fileOperations.push(fileOperation);
      }
    }

    return fileOperations;
  }

  /**
   * 检查工作流执行是否可能重复（在执行前调用）
   */
  async checkPotentialDuplicate(workflow: Workflow, files: AppFile[]): Promise<{ isDuplicate: boolean; message?: string }> {
    const executionHash = this.generateExecutionHash({
      workflowId: workflow.id,
      stepResults: workflow.steps.map(step => ({
        stepId: step.id,
        stepName: step.name,
        inputFiles: [],
        outputFiles: [],
        processedCount: 0,
        errors: [],
        duration: 0
      })),
      totalFiles: files.length,
      processedFiles: 0,
      errors: [],
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      duration: 0,
      changes: []
    }, workflow, files);

    const isDuplicate = this.isDuplicateExecution(executionHash, new Date().toISOString());

    if (isDuplicate) {
      return {
        isDuplicate: true,
        message: `检测到可能的重复执行：工作流"${workflow.name}"在短时间内重复执行相同的文件集合。请确认是否继续执行。`
      };
    }

    return { isDuplicate: false };
  }

  /**
   * 搜索历史记录（使用内存缓存）
   */
  async searchEntries(query: string, limit?: number): Promise<HistoryEntry[]> {
    const history = await this.getEntries(); // 使用缓存的获取方法
    const lowerQuery = query.toLowerCase();

    const filtered = history.filter((entry: HistoryEntry) =>
      entry.workflowName.toLowerCase().includes(lowerQuery) ||
      entry.stepName?.toLowerCase().includes(lowerQuery) ||
      entry.monitorTaskName?.toLowerCase().includes(lowerQuery) ||
      entry.fileOperations.some((op: FileOperation) =>
        op.originalName.toLowerCase().includes(lowerQuery) ||
        op.newName?.toLowerCase().includes(lowerQuery)
      )
    );

    return limit ? filtered.slice(0, limit) : filtered;
  }

  /**
   * 清空历史记录
   */
  async clearHistory(): Promise<void> {
    await saveHistory([]);
    this.memoryCache = [];
  }

  /**
   * 删除单条历史记录
   */
  async deleteEntry(entryId: string): Promise<boolean> {
    const history = await this.getEntries(); // 使用缓存
    const filteredHistory = history.filter((entry: HistoryEntry) => entry.id !== entryId);

    if (filteredHistory.length !== history.length) {
      await saveHistory(filteredHistory);
      this.memoryCache = filteredHistory; // 更新缓存
      return true;
    }

    return false;
  }

  /**
   * 检查文件/文件夹权限（复用工作流引擎的权限检查）
   */
  private async checkPermissions(filePath: string, operation: 'read' | 'write'): Promise<{ hasPermission: boolean; error?: string }> {
    try {
      const hasPermission = await this.workflowEngine.checkPermissions(filePath, operation);
      if (hasPermission) {
        return { hasPermission: true };
      } else {
        return {
          hasPermission: false,
          error: `${operation === 'read' ? '读取' : '写入'}权限不足`
        };
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      return {
        hasPermission: false,
        error: `权限检查失败: ${errorMsg}`
      };
    }
  }

  /**
   * 检查目录权限（包括父目录）
   */
  private async checkDirectoryPermissions(dirPath: string): Promise<{ hasPermission: boolean; error?: string }> {
    try {
      // 检查目录是否存在
      if (await fs.pathExists(dirPath)) {
        // 目录存在，检查写入权限
        return await this.checkPermissions(dirPath, 'write');
      } else {
        // 目录不存在，检查父目录的写入权限
        const parentDir = require('path').dirname(dirPath);
        if (parentDir === dirPath) {
          // 已经到根目录
          return { hasPermission: false, error: '无法访问根目录' };
        }
        return await this.checkDirectoryPermissions(parentDir);
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      return { hasPermission: false, error: `目录权限检查失败: ${errorMsg}` };
    }
  }

  /**
   * 检查磁盘空间是否足够（复用工作流引擎的磁盘空间检查）
   */
  private async checkDiskSpace(targetPath: string, requiredSize: number): Promise<{ hasSpace: boolean; error?: string }> {
    return await this.workflowEngine.checkDiskSpace(targetPath, requiredSize);
  }

  /**
   * 计算撤回操作所需的磁盘空间
   */
  private calculateRequiredSpace(operations: FileOperation[]): number {
    let totalSize = 0;

    for (const operation of operations) {
      if (operation.status === 'success') {
        // 对于移动和重命名操作，通常不需要额外空间
        // 对于复制操作的撤回（删除），会释放空间
        // 这里主要考虑可能的临时文件空间需求
        if (operation.operation === 'move' || operation.operation === 'rename') {
          // 移动操作可能需要临时空间（如果跨分区）
          totalSize += operation.fileSize;
        }
      }
    }

    return totalSize;
  }

  /**
   * 分类和格式化错误信息（复用工作流引擎的错误分类）
   */
  private categorizeError(error: Error, operation: string, filePath: string): string {
    return this.workflowEngine.categorizeError(error, operation, filePath);
  }

  /**
   * 生成用户友好的错误建议（增强版）
   */
  private generateErrorSuggestion(error: string, context?: { operation?: string; filePath?: string; retryCount?: number }): string {
    const baseError = error.toLowerCase();
    let suggestion = '';
    let priority = 'medium';

    // 权限相关错误
    if (baseError.includes('权限不足') || baseError.includes('eacces') || baseError.includes('eperm')) {
      priority = 'high';
      suggestion = '💡 权限问题解决方案：\n' +
        '  1. 以管理员身份运行程序\n' +
        '  2. 检查文件/文件夹权限设置\n' +
        '  3. 确保当前用户有足够的访问权限';

      if (context?.filePath) {
        suggestion += `\n  4. 检查路径权限: ${context.filePath}`;
      }
    }
    // 文件占用错误
    else if (baseError.includes('文件被占用') || baseError.includes('ebusy') || baseError.includes('正在使用')) {
      priority = 'high';
      suggestion = '💡 文件占用解决方案：\n' +
        '  1. 关闭正在使用该文件的程序\n' +
        '  2. 检查是否有其他进程在访问文件\n' +
        '  3. 等待几秒后重试\n' +
        '  4. 重启相关应用程序';
    }
    // 磁盘空间错误
    else if (baseError.includes('磁盘空间不足') || baseError.includes('enospc') || baseError.includes('空间')) {
      priority = 'high';
      suggestion = '💡 磁盘空间解决方案：\n' +
        '  1. 清理磁盘空间（删除临时文件、回收站等）\n' +
        '  2. 选择其他有足够空间的位置\n' +
        '  3. 检查磁盘使用情况\n' +
        '  4. 考虑移动大文件到其他位置';
    }
    // 文件不存在错误
    else if (baseError.includes('文件不存在') || baseError.includes('enoent') || baseError.includes('找不到')) {
      priority = 'medium';
      suggestion = '💡 文件缺失解决方案：\n' +
        '  1. 检查文件是否被手动删除或移动\n' +
        '  2. 确认文件路径是否正确\n' +
        '  3. 检查是否有其他程序移动了文件\n' +
        '  4. 考虑从备份恢复文件';
    }
    // 目标已存在错误
    else if (baseError.includes('目标已存在') || baseError.includes('eexist') || baseError.includes('已存在')) {
      priority = 'medium';
      suggestion = '💡 文件冲突解决方案：\n' +
        '  1. 检查目标位置是否有同名文件\n' +
        '  2. 重命名冲突的文件\n' +
        '  3. 选择不同的目标位置\n' +
        '  4. 确认是否要覆盖现有文件';
    }
    // 网络相关错误
    else if (baseError.includes('网络') || baseError.includes('连接') || baseError.includes('超时')) {
      priority = 'medium';
      suggestion = '💡 网络问题解决方案：\n' +
        '  1. 检查网络连接状态\n' +
        '  2. 确认网络路径是否可访问\n' +
        '  3. 重试操作\n' +
        '  4. 检查防火墙设置';
    }
    // 路径相关错误
    else if (baseError.includes('路径') || baseError.includes('path') || baseError.includes('目录')) {
      priority = 'medium';
      suggestion = '💡 路径问题解决方案：\n' +
        '  1. 检查路径格式是否正确\n' +
        '  2. 确认路径长度不超过系统限制\n' +
        '  3. 检查路径中是否包含特殊字符\n' +
        '  4. 确认目录结构是否完整';
    }
    // 通用错误
    else {
      suggestion = '💡 通用解决方案：\n' +
        '  1. 检查文件状态和系统环境\n' +
        '  2. 重启应用程序后重试\n' +
        '  3. 检查系统资源使用情况\n' +
        '  4. 必要时手动恢复文件';
    }

    // 添加重试建议
    if (context?.retryCount && context.retryCount > 0) {
      suggestion += `\n\n⚠️ 已重试 ${context.retryCount} 次，建议检查根本原因`;
    }

    // 添加操作上下文
    if (context?.operation) {
      suggestion += `\n\n📋 操作类型: ${context.operation}`;
    }

    // 添加优先级标识
    const priorityIcon = priority === 'high' ? '🔴' : priority === 'medium' ? '🟡' : '🟢';
    suggestion = `${priorityIcon} ${suggestion}`;

    return suggestion;
  }

  /**
   * 创建结构化错误报告
   */
  private createErrorReport(errors: string[], context: { operation: string; entryId?: string; timestamp?: string }): {
    summary: string;
    details: string[];
    suggestions: string[];
    severity: 'low' | 'medium' | 'high';
    canRetry: boolean;
  } {
    if (errors.length === 0) {
      return {
        summary: '操作成功完成',
        details: [],
        suggestions: [],
        severity: 'low',
        canRetry: false
      };
    }

    // 分析错误严重程度
    let severity: 'low' | 'medium' | 'high' = 'low';
    let canRetry = true;

    const criticalErrors = errors.filter(error =>
      error.includes('权限不足') ||
      error.includes('磁盘空间') ||
      error.includes('系统错误')
    );

    const mediumErrors = errors.filter(error =>
      error.includes('文件被占用') ||
      error.includes('文件不存在') ||
      error.includes('目标已存在')
    );

    if (criticalErrors.length > 0) {
      severity = 'high';
      canRetry = false;
    } else if (mediumErrors.length > 0) {
      severity = 'medium';
      canRetry = true;
    }

    // 生成摘要
    const summary = errors.length === 1
      ? `${context.operation}过程中发生1个错误`
      : `${context.operation}过程中发生${errors.length}个错误`;

    // 提取建议
    const suggestions = errors.map(error => this.generateErrorSuggestion(error, { operation: context.operation }))
      .filter((suggestion, index, array) => array.indexOf(suggestion) === index); // 去重

    return {
      summary,
      details: errors,
      suggestions,
      severity,
      canRetry
    };
  }

  /**
   * 事务性更新历史记录条目状态
   */
  private async updateHistoryEntryStatus(
    entryId: string,
    updates: Partial<Pick<HistoryEntry, 'isUndone' | 'undoTimestamp' | 'canUndo'>>
  ): Promise<void> {
    try {
      // 重新加载最新的历史记录，避免并发修改冲突
      const history = await loadHistory();
      const entryIndex = history.findIndex((entry: HistoryEntry) => entry.id === entryId);

      if (entryIndex === -1) {
        throw new Error(`历史记录条目不存在: ${entryId}`);
      }

      // 更新条目状态
      history[entryIndex] = {
        ...history[entryIndex],
        ...updates
      };

      // 原子性保存
      await saveHistory(history);

      // 清除内存缓存，确保前端获取到最新状态
      this.clearMemoryCache();

      console.log(`✅ 历史记录状态已更新: ${entryId}`, updates);
    } catch (error) {
      console.error(`❌ 更新历史记录状态失败: ${entryId}`, error);
      throw new Error(`更新历史记录状态失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 手动触发清理
   */
  async manualCleanup(): Promise<{ deletedCount: number; message: string }> {
    const history = await loadHistory();
    const beforeCount = history.length;

    const cleanedHistory = this.performCleanup(history);
    const deletedCount = beforeCount - cleanedHistory.length;

    if (deletedCount > 0) {
      await saveHistory(cleanedHistory);
      this.memoryCache = cleanedHistory;
      return {
        deletedCount,
        message: `已清理${deletedCount}条历史记录`
      };
    }

    return {
      deletedCount: 0,
      message: '无需清理'
    };
  }

  /**
   * 撤销历史记录操作
   */
  async undoEntry(entryId: string): Promise<{
    success: boolean;
    message?: string;
    requiresChainUndo?: boolean;
    entryId?: string
  }> {
    // 获取操作锁，防止并发撤回同一条记录
    await this.acquireOperationLock(entryId);

    const operationPromise = this.performUndoEntry(entryId);
    this.operationLocks.set(entryId, operationPromise);
    this.operationLockTimestamps.set(entryId, Date.now());

    try {
      const result = await operationPromise;
      return result;
    } finally {
      this.releaseOperationLock(entryId);
    }
  }

  /**
   * 执行撤销操作的核心逻辑
   */
  private async performUndoEntry(entryId: string): Promise<{
    success: boolean;
    message?: string;
    requiresChainUndo?: boolean;
    entryId?: string
  }> {
    try {
      console.log('开始撤销操作，entryId:', entryId);

      await this.acquireHistoryLock();
      let history: HistoryEntry[];
      try {
        history = await loadHistory();
        console.log('当前历史记录数量:', history.length);
      } finally {
        this.releaseHistoryLock();
      }

      const entryIndex = history.findIndex((entry: HistoryEntry) => entry.id === entryId);

      if (entryIndex === -1) {
        return { success: false, message: '历史记录不存在，可能已被删除' };
      }

      const entry = history[entryIndex];
      console.log('找到要撤销的记录:', entry.workflowName);

      // 检查是否可以撤销（兼容旧数据，canUndo为undefined时默认可以撤销）
      if (entry.canUndo === false) {
        return { success: false, message: '此操作被标记为不可撤销' };
      }

      if (entry.isUndone) {
        return { success: false, message: '此操作已经被撤销过了' };
      }

      // 检查撤销时间限制（24小时）
      const entryTime = new Date(entry.timestamp).getTime();
      const now = Date.now();
      const hoursDiff = (now - entryTime) / (1000 * 60 * 60);

      if (hoursDiff > 24) {
        const hoursAgo = Math.floor(hoursDiff);
        return {
          success: false,
          message: `操作已过去 ${hoursAgo} 小时，超过24小时时间限制，无法撤销。请手动恢复文件。`
        };
      }

      // 预检查撤销操作的可行性
      const preCheckResult = await this.preCheckUndoOperations(entry.fileOperations);
      if (!preCheckResult.canUndo) {
        // 检查是否是连锁重命名冲突
        const hasChainConflict = preCheckResult.issues.some(issue => issue.includes('[连锁冲突]'));

        if (hasChainConflict) {
          // 提供连锁撤回选项
          return {
            success: false,
            message: `检测到连锁重命名冲突:\n${preCheckResult.issues.join('\n')}\n\n解决方案：\n1. 使用连锁撤回功能自动处理依赖关系\n2. 手动逐个撤回相关操作\n3. 手动恢复文件位置`,
            requiresChainUndo: true,
            entryId: entryId
          };
        } else {
          // 分析错误类型并提供针对性建议
          const hasPermissionIssues = preCheckResult.issues.some(issue => issue.includes('权限不足'));
          const hasSpaceIssues = preCheckResult.issues.some(issue => issue.includes('磁盘空间'));
          const hasFileIssues = preCheckResult.issues.some(issue => issue.includes('不存在') || issue.includes('已被占用'));

          let suggestion = '';
          if (hasPermissionIssues) {
            suggestion = '\n💡 解决方案：请以管理员身份运行程序，或检查文件权限设置';
          } else if (hasSpaceIssues) {
            suggestion = '\n💡 解决方案：请清理磁盘空间或等待其他操作完成';
          } else if (hasFileIssues) {
            suggestion = '\n💡 解决方案：请检查文件是否被手动移动或删除，考虑手动恢复';
          } else {
            suggestion = '\n💡 解决方案：请检查系统状态，必要时手动恢复文件';
          }

          return {
            success: false,
            message: `撤销预检查失败:\n${preCheckResult.issues.join('\n')}${suggestion}`
          };
        }
      }

      // 执行撤销操作 - 使用事务性处理确保数据一致性
      console.log('开始执行文件撤销操作');

      const operationId = `undo-${entryId}-${Date.now()}`;

      try {
        // 预验证所有操作的路径安全性
        for (const operation of entry.fileOperations) {
          if (operation.status !== 'success') continue;

          const pathValidation = this.normalizeOperationPaths(operation);
          if (!pathValidation.isValid) {
            throw new Error(`路径安全验证失败: ${pathValidation.error}`);
          }
        }

        await this.performUndoOperations(entry.fileOperations, operationId);
        console.log('文件撤销操作完成');

        // 清理工作流执行过程中创建的文件夹
        console.log('🔍 检查文件夹清理条件:', {
          hasCreatedDirectories: !!entry.createdDirectories,
          createdDirectoriesLength: entry.createdDirectories?.length || 0,
          createdDirectories: entry.createdDirectories
        });

        if (entry.createdDirectories && entry.createdDirectories.length > 0) {
          console.log('开始清理工作流创建的文件夹');
          await this.cleanupCreatedDirectories(entry.createdDirectories);
          console.log('文件夹清理完成');
        } else {
          console.log('⚠️ 没有需要清理的文件夹或文件夹列表为空');
        }

        // 恢复被清理的空文件夹
        console.log('🔍 检查空文件夹恢复条件:', {
          hasCleanedEmptyDirectories: !!entry.cleanedEmptyDirectories,
          cleanedEmptyDirectoriesLength: entry.cleanedEmptyDirectories?.length || 0,
          cleanedEmptyDirectories: entry.cleanedEmptyDirectories
        });

        if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
          console.log('开始恢复被清理的空文件夹');
          await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
          console.log('空文件夹恢复完成');
        } else {
          console.log('⚠️ 没有需要恢复的空文件夹或文件夹列表为空');
        }

        // 记录历史记录更新前的状态，用于回滚
        if (operationId) {
          this.logOperationStep(operationId, {
            id: `history-update-${Date.now()}`,
            type: 'history_update',
            metadata: { originalEntry: { ...entry } },
            timestamp: Date.now(),
            completed: false
          });
        }

        // 事务性更新历史记录状态
        await this.updateHistoryEntryStatus(entryId, {
          isUndone: true,
          undoTimestamp: new Date().toISOString(),
          canUndo: false
        });

        // 标记历史记录更新完成
        if (operationId) {
          const steps = this.operationLog.get(operationId);
          if (steps) {
            const lastStep = steps[steps.length - 1];
            if (lastStep && lastStep.type === 'history_update') {
              lastStep.completed = true;
            }
          }
        }

        console.log('撤销操作完成，历史记录已更新');

        // 清理操作日志
        if (operationId) {
          this.operationLog.delete(operationId);
        }
      } catch (undoError) {
        // 撤销操作失败，执行回滚
        console.error('撤销操作失败，开始回滚操作');

        if (operationId) {
          try {
            await this.rollbackOperation(operationId);
            console.log('回滚操作完成');
          } catch (rollbackError) {
            console.error('回滚操作也失败了:', rollbackError);
          }
        }

        throw undoError;
      }

      return { success: true, message: '撤销操作成功完成' };
    } catch (error) {
      console.error('撤销操作失败:', error);

      // 提供更详细的错误信息
      let errorMessage = '撤销操作失败';
      if (error instanceof Error) {
        if (error.message.includes('ENOENT')) {
          errorMessage = '撤销失败：相关文件或文件夹不存在，可能已被手动删除或移动';
        } else if (error.message.includes('EACCES') || error.message.includes('EPERM')) {
          errorMessage = '撤销失败：权限不足，请以管理员身份运行或检查文件权限';
        } else if (error.message.includes('EBUSY')) {
          errorMessage = '撤销失败：文件正在被其他程序使用，请关闭相关程序后重试';
        } else if (error.message.includes('EEXIST')) {
          errorMessage = '撤销失败：目标位置已存在同名文件或文件夹';
        } else {
          errorMessage = `撤销失败：${error.message}`;
        }
      }

      return { success: false, message: errorMessage };
    }
  }

  /**
   * 连锁撤回操作 - 处理连锁重命名冲突
   */
  async chainUndoEntry(entryId: string): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('开始连锁撤回操作，entryId:', entryId);
      const history = await loadHistory();

      const entryIndex = history.findIndex((entry: HistoryEntry) => entry.id === entryId);
      if (entryIndex === -1) {
        return { success: false, message: '历史记录不存在，可能已被删除' };
      }

      const entry = history[entryIndex];
      console.log('找到要连锁撤回的记录:', entry.workflowName);

      // 检查基本撤回条件
      if (entry.canUndo === false) {
        return { success: false, message: '此操作被标记为不可撤销' };
      }

      if (entry.isUndone) {
        return { success: false, message: '此操作已经被撤销过了' };
      }

      // 检查时间限制
      const entryTime = new Date(entry.timestamp).getTime();
      const now = Date.now();
      const hoursDiff = (now - entryTime) / (1000 * 60 * 60);

      if (hoursDiff > 24) {
        const hoursAgo = Math.floor(hoursDiff);
        return {
          success: false,
          message: `操作已过去 ${hoursAgo} 小时，超过24小时时间限制，无法撤销。`
        };
      }

      // 分析连锁依赖关系
      const chainAnalysis = await this.analyzeChainDependencies(entry.fileOperations);
      console.log('连锁依赖分析结果:', chainAnalysis);

      if (chainAnalysis.conflicts.length === 0) {
        // 没有连锁冲突，执行普通撤回操作（避免递归调用）
        console.log('开始执行普通撤回操作');
        await this.performUndoOperations(entry.fileOperations);
        console.log('普通撤回操作完成');

        // 清理工作流创建的文件夹
        if (entry.createdDirectories && entry.createdDirectories.length > 0) {
          console.log('开始清理工作流创建的文件夹');
          await this.cleanupCreatedDirectories(entry.createdDirectories);
          console.log('文件夹清理完成');
        }

        // 恢复被清理的空文件夹
        if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
          console.log('开始恢复被清理的空文件夹');
          await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
          console.log('空文件夹恢复完成');
        }

        // 更新历史记录状态
        const history = await loadHistory();
        const entryIndex = history.findIndex((e: HistoryEntry) => e.id === entryId);
        if (entryIndex !== -1) {
          history[entryIndex] = {
            ...entry,
            isUndone: true,
            undoTimestamp: new Date().toISOString(),
            canUndo: false
          };
          await saveHistory(history);
          this.clearMemoryCache();
        }

        return { success: true, message: '撤回操作成功完成' };
      }

      // 执行连锁撤回
      console.log('开始执行连锁撤回操作');
      await this.performChainUndoOperations(entry.fileOperations, chainAnalysis);
      console.log('连锁撤回操作完成');

      // 清理工作流创建的文件夹
      if (entry.createdDirectories && entry.createdDirectories.length > 0) {
        console.log('开始清理工作流创建的文件夹');
        await this.cleanupCreatedDirectories(entry.createdDirectories);
        console.log('文件夹清理完成');
      }

      // 恢复被清理的空文件夹
      if (entry.cleanedEmptyDirectories && entry.cleanedEmptyDirectories.length > 0) {
        console.log('开始恢复被清理的空文件夹');
        await this.restoreCleanedEmptyDirectories(entry.cleanedEmptyDirectories);
        console.log('空文件夹恢复完成');
      }

      // 事务性更新历史记录状态
      await this.updateHistoryEntryStatus(entryId, {
        isUndone: true,
        undoTimestamp: new Date().toISOString(),
        canUndo: false
      });

      return { success: true, message: '连锁撤回操作成功完成' };
    } catch (error) {
      console.error('连锁撤回操作失败:', error);

      let errorMessage = '连锁撤回操作失败';
      if (error instanceof Error) {
        errorMessage = `连锁撤回失败：${error.message}`;
      }

      return { success: false, message: errorMessage };
    }
  }

  /**
   * 重做已撤销的操作
   */
  async redoEntry(entryId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const history = await loadHistory();
      const entryIndex = history.findIndex((entry: HistoryEntry) => entry.id === entryId);

      if (entryIndex === -1) {
        return { success: false, message: '历史记录不存在' };
      }

      const entry = history[entryIndex];

      // 检查是否可以重做
      if (!entry.isUndone) {
        return { success: false, message: '此操作无法重做' };
      }

      // 重做操作预检查
      const preCheckResult = await this.preCheckRedoOperations(entry.fileOperations);
      if (!preCheckResult.canRedo) {
        // 分析错误类型并提供针对性建议
        const hasPermissionIssues = preCheckResult.issues.some(issue => issue.includes('权限不足'));
        const hasSpaceIssues = preCheckResult.issues.some(issue => issue.includes('磁盘空间'));
        const hasConflictIssues = preCheckResult.issues.some(issue => issue.includes('已被占用') || issue.includes('冲突'));

        let suggestion = '';
        if (hasPermissionIssues) {
          suggestion = '\n💡 解决方案：请以管理员身份运行程序，或检查文件权限设置';
        } else if (hasSpaceIssues) {
          suggestion = '\n💡 解决方案：请清理磁盘空间后重试';
        } else if (hasConflictIssues) {
          suggestion = '\n💡 解决方案：请检查目标位置是否有同名文件，考虑手动处理冲突';
        } else {
          suggestion = '\n💡 解决方案：请检查系统状态，确保文件系统处于稳定状态';
        }

        return {
          success: false,
          message: `重做预检查失败:\n${preCheckResult.issues.join('\n')}${suggestion}`
        };
      }

      // 执行重做操作
      await this.performRedoOperations(entry.fileOperations);

      // 更新原历史记录状态（原地更新，不创建新记录）
      history[entryIndex] = {
        ...entry,
        isUndone: false,
        canUndo: true,
        undoTimestamp: undefined
      };

      await saveHistory(history);

      // 清除内存缓存，确保前端获取到最新状态
      this.clearMemoryCache();

      return { success: true };
    } catch (error) {
      console.error('重做操作失败:', error);
      return { success: false, message: `重做操作失败: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * 执行连锁撤回操作
   */
  private async performChainUndoOperations(
    operations: FileOperation[],
    chainAnalysis: { conflicts: Array<{ operation: FileOperation; blockingOperation: FileOperation }>; executionOrder: FileOperation[] }
  ): Promise<void> {
    const errors: string[] = [];
    const warnings: string[] = [];

    console.log(`开始执行连锁撤回，共 ${chainAnalysis.executionOrder.length} 个操作`);
    console.log('执行顺序:', chainAnalysis.executionOrder.map(op => `${op.originalName} (${op.originalPath} -> ${op.newPath})`));

    // 使用临时路径避免冲突
    const tempMappings = new Map<string, string>();

    // 第一阶段：将所有冲突的文件移动到临时位置
    for (const operation of chainAnalysis.executionOrder) {
      if (!operation.newPath || !operation.originalPath) continue;

      try {
        // 检查是否存在冲突
        const hasConflict = chainAnalysis.conflicts.some(c => c.operation.id === operation.id);

        if (hasConflict && await fs.pathExists(operation.originalPath)) {
          // 创建临时路径
          const tempName = `chain-undo-temp-${operation.id}-${Date.now()}`;
          const tempPath = require('path').join(
            require('path').dirname(operation.originalPath),
            tempName
          );

          // 将占用目标位置的文件移动到临时位置
          console.log(`🔄 临时移动冲突文件: ${operation.originalPath} -> ${tempPath}`);
          await fs.move(operation.originalPath, tempPath);
          tempMappings.set(operation.originalPath, tempPath);
        }
      } catch (error) {
        const errorMsg = `临时移动文件失败 ${operation.originalPath}: ${error instanceof Error ? error.message : String(error)}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }

    // 第二阶段：执行实际的撤回操作
    for (const operation of chainAnalysis.executionOrder) {
      try {
        switch (operation.operation) {
          case 'move':
          case 'rename':
            await this.undoMoveOrRename(operation, errors, warnings);
            break;

          case 'copy':
            await this.undoCopy(operation, errors, warnings);
            break;

          case 'delete':
            warnings.push(`删除操作无法撤销，请从回收站手动恢复: ${operation.originalPath}`);
            break;

          case 'createFolder':
            await this.undoCreateFolder(operation, errors, warnings);
            break;
        }
      } catch (error) {
        const categorizedError = error instanceof Error
          ? this.categorizeError(error, '连锁撤回', operation.originalPath)
          : `连锁撤回操作失败 ${operation.originalPath}: ${String(error)}`;

        const suggestion = this.generateErrorSuggestion(categorizedError, {
          operation: '连锁撤回',
          filePath: operation.originalPath,
          retryCount: 0
        });
        const fullErrorMsg = `${categorizedError}\n${suggestion}`;

        console.error(fullErrorMsg);
        errors.push(fullErrorMsg);
      }
    }

    // 第三阶段：清理临时文件
    for (const [originalPath, tempPath] of tempMappings) {
      try {
        if (await fs.pathExists(tempPath)) {
          console.log(`🧹 清理临时文件: ${tempPath}`);
          await fs.remove(tempPath);
        }
      } catch (error) {
        console.warn(`清理临时文件失败 ${tempPath}:`, error);
      }
    }

    // 处理错误和警告
    if (errors.length > 0) {
      let errorMessage = `连锁撤回过程中发生错误:\n${errors.join('\n')}`;
      if (warnings.length > 0) {
        errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
      }
      throw new Error(errorMessage);
    } else if (warnings.length > 0) {
      console.warn(`连锁撤回完成，但有警告:\n${warnings.join('\n')}`);
    }
  }

  /**
   * 执行撤销操作
   */
  private async performUndoOperations(operations: FileOperation[], operationId?: string): Promise<void> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 只撤销成功的操作
    const successfulOperations = operations.filter(op => op.status === 'success');
    console.log(`开始撤销 ${successfulOperations.length} 个成功的操作（跳过 ${operations.length - successfulOperations.length} 个失败的操作）`);

    // 如果提供了操作ID，记录历史记录更新步骤用于回滚
    if (operationId) {
      this.logOperationStep(operationId, {
        id: `history-backup-${Date.now()}`,
        type: 'history_update',
        metadata: { operationType: 'undo_start' },
        timestamp: Date.now(),
        completed: false
      });
    }

    for (const operation of successfulOperations) {
      try {
        switch (operation.operation) {
          case 'move':
          case 'rename':
            await this.undoMoveOrRename(operation, errors, warnings);
            break;

          case 'copy':
            await this.undoCopy(operation, errors, warnings);
            break;

          case 'delete':
            // 删除操作无法撤销，文件已移至回收站，用户可手动恢复
            warnings.push(`删除操作无法撤销，请从回收站手动恢复: ${operation.originalPath}`);
            break;

          case 'createFolder':
            await this.undoCreateFolder(operation, errors, warnings);
            break;
        }
      } catch (error) {
        const categorizedError = error instanceof Error
          ? this.categorizeError(error, '撤销', operation.originalPath)
          : `撤销操作失败 ${operation.originalPath}: ${String(error)}`;

        const suggestion = this.generateErrorSuggestion(categorizedError, {
          operation: '撤销',
          filePath: operation.originalPath,
          retryCount: 0
        });
        const fullErrorMsg = `${categorizedError}\n${suggestion}`;

        console.error(fullErrorMsg);
        errors.push(fullErrorMsg);
      }
    }

    // 处理错误和警告
    if (errors.length > 0) {
      let errorMessage = `撤销过程中发生错误:\n${errors.join('\n')}`;
      if (warnings.length > 0) {
        errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
      }
      throw new Error(errorMessage);
    } else if (warnings.length > 0) {
      console.warn(`撤销完成，但有警告:\n${warnings.join('\n')}`);
    }
  }

  /**
   * 撤销移动或重命名操作 (终极修复版：采用“偏执”的两步法)
   */
  private async undoMoveOrRename(operation: FileOperation, errors: string[], warnings: string[]): Promise<void> {
    const sourcePath = operation.newPath;
    const finalDestPath = operation.originalPath;

    console.log(`[防御性撤销] 准备执行: ${sourcePath} -> ${finalDestPath}`);

    if (!sourcePath || !finalDestPath) {
        errors.push(`撤销失败：操作记录无效，源或目标路径缺失。`);
        return;
    }

    // 步骤 1: 检查源文件/文件夹是否存在。
    if (!await fs.pathExists(sourcePath)) {
        errors.push(`撤销失败：源文件/文件夹已不存在于 ${sourcePath}`);
        return;
    }

    // 步骤 2: 再次确认最终目标位置是否已被占用。这是核心防御。
    if (await fs.pathExists(finalDestPath)) {
        errors.push(`撤销失败：目标路径 ${finalDestPath} 已被占用，无法继续。`);
        return;
    }

    // 步骤 3: 确保目标位置的父目录存在。
    const destParentDir = require('path').dirname(finalDestPath);
    try {
        await fs.ensureDir(destParentDir);
    } catch (err) {
        const errorMsg = err instanceof Error ? err.message : String(err);
        errors.push(`撤销失败：无法创建父目录 ${destParentDir}。错误: ${errorMsg}`);
        return;
    }

    // 步骤 4: 执行“偏执”的两步移动，彻底规避嵌套问题。
    // 创建一个唯一的临时路径，用于中转。
    const tempName = `undo-temp-${operation.id}-${Date.now()}`;
    const tempPath = require('path').join(destParentDir, tempName);

    try {
        // 第 A 步：将源移动到一个保证不存在的临时路径下。
        // 这个 move 操作绝不会有歧义。
        await fs.move(sourcePath, tempPath);

        // 第 B 步：将临时文件/文件夹重命名为最终名称。
        // fs.rename 是一个更原子性的操作，如果目标已存在，它会直接失败而不是嵌套。
        await fs.rename(tempPath, finalDestPath);

        console.log(`✅ 成功撤销: ${sourcePath} -> ${finalDestPath}`);

    } catch (err) {
        const errorMsg = `撤销失败：在移动/重命名过程中发生错误。目标: ${finalDestPath}. 错误: ${err instanceof Error ? err.message : String(err)}`;
        console.error(errorMsg, err);
        errors.push(errorMsg);

        // 如果发生错误，尝试清理残留的临时文件/文件夹。
        if (await fs.pathExists(tempPath)) {
            await fs.remove(tempPath).catch(cleanupErr => {
                console.error(`!!! 清理临时文件中转失败: ${tempPath}`, cleanupErr);
            });
        }
    }
  }

  /**
   * 撤销创建文件夹操作
   */
  private async undoCreateFolder(operation: FileOperation, errors: string[], warnings: string[]): Promise<void> {
    const folderPath = operation.newPath || operation.originalPath;

    if (!folderPath) {
      errors.push(`操作记录不完整，缺少文件夹路径: ${operation.originalName}`);
      return;
    }

    if (!await fs.pathExists(folderPath)) {
      warnings.push(`要删除的文件夹不存在（可能已被手动删除）: ${folderPath}`);
      return;
    }

    try {
      // 检查文件夹是否为空
      const items = await fs.readdir(folderPath);
      if (items.length > 0) {
        warnings.push(`文件夹不为空，无法撤销创建操作: ${folderPath} (包含 ${items.length} 个项目)`);
        return;
      }

      // 删除空文件夹
      await fs.rmdir(folderPath);
      console.log(`✅ 成功撤销文件夹创建，已删除: ${folderPath}`);
    } catch (removeError) {
      const errorMsg = `删除创建的文件夹失败: ${folderPath}`;
      console.error(errorMsg, removeError);
      errors.push(`${errorMsg}: ${removeError instanceof Error ? removeError.message : String(removeError)}`);
    }
  }

  /**
   * 撤销复制操作
   */
  private async undoCopy(operation: FileOperation, errors: string[], warnings: string[]): Promise<void> {
    if (!operation.newPath) {
      errors.push(`操作记录不完整，缺少新路径: ${operation.originalPath}`);
      return;
    }

    if (!await fs.pathExists(operation.newPath)) {
      warnings.push(`要删除的复制文件/文件夹不存在（可能已被手动删除）: ${operation.newPath}`);
      return;
    }

    try {
      // 获取文件/文件夹信息以确定类型
      const stat = await fs.stat(operation.newPath);
      const isDirectory = stat.isDirectory();

      // 删除复制的文件/文件夹
      await fs.remove(operation.newPath);

      console.log(`✅ 成功撤销${isDirectory ? '文件夹' : '文件'}复制，已删除: ${operation.newPath}`);
    } catch (removeError) {
      const errorMsg = `删除复制的文件/文件夹失败: ${operation.newPath}`;
      console.error(errorMsg, removeError);
      errors.push(`${errorMsg}: ${removeError instanceof Error ? removeError.message : String(removeError)}`);
    }
  }

  /**
   * 预检查重做操作的可行性
   */
  private async preCheckRedoOperations(operations: FileOperation[]): Promise<{ canRedo: boolean; issues: string[] }> {
    const issues: string[] = [];
    const successfulOperations = operations.filter(op => op.status === 'success');

    // 检查磁盘空间
    const requiredSpace = this.calculateRequiredSpace(successfulOperations);
    if (requiredSpace > 0) {
      // 检查主要目标路径的磁盘空间
      const moveOperations = successfulOperations.filter(op =>
        (op.operation === 'move' || op.operation === 'rename' || op.operation === 'copy') && op.newPath
      );

      for (const operation of moveOperations.slice(0, 3)) { // 只检查前3个，避免过多检查
        if (operation.newPath) {
          const spaceCheck = await this.checkDiskSpace(operation.newPath, requiredSpace);
          if (!spaceCheck.hasSpace) {
            issues.push(`[重做预检警告] ${spaceCheck.error}`);
            break; // 只报告一次磁盘空间问题
          }
        }
      }
    }

    for (const operation of successfulOperations) {
      const sourcePath = operation.originalPath; // 重做时，源是原始路径
      const targetPath = operation.newPath;       // 目标是新路径

      try {
        switch (operation.operation) {
          case 'move':
          case 'rename':
            if (!sourcePath || !targetPath) {
              issues.push(`[重做预检失败] 操作记录无效: ${operation.originalName}`);
              continue;
            }

            // 检查源文件是否存在
            if (!await fs.pathExists(sourcePath)) {
              issues.push(`[重做预检失败] 源文件/文件夹不存在: ${sourcePath}`);
              continue;
            }

            // 检查目标位置是否已被占用
            if (await fs.pathExists(targetPath)) {
              issues.push(`[重做预检失败] 目标位置已被占用: ${targetPath}`);
              continue;
            }

            // 检查源文件的读取权限
            const sourcePermCheck = await this.checkPermissions(sourcePath, 'read');
            if (!sourcePermCheck.hasPermission) {
              issues.push(`[重做预检失败] 源文件/文件夹权限不足: ${sourcePath} - ${sourcePermCheck.error}`);
              continue;
            }

            // 检查目标位置的写入权限
            const targetDir = require('path').dirname(targetPath);
            const targetPermCheck = await this.checkDirectoryPermissions(targetDir);
            if (!targetPermCheck.hasPermission) {
              issues.push(`[重做预检失败] 目标目录权限不足: ${targetDir} - ${targetPermCheck.error}`);
              continue;
            }

            // 检查是否会形成循环移动（移动到自己的子目录）
            const normalizedSource = require('path').normalize(sourcePath);
            const normalizedTarget = require('path').normalize(targetPath);
            if (normalizedTarget.startsWith(normalizedSource + require('path').sep)) {
              issues.push(`[重做预检失败] 不能将文件夹移动到自己内部: ${sourcePath} -> ${targetPath}`);
              continue;
            }
            break;

          case 'copy':
            if (!sourcePath || !targetPath) {
              issues.push(`[重做预检失败] 复制操作记录无效: ${operation.originalName}`);
              continue;
            }

            // 检查源文件是否存在
            if (!await fs.pathExists(sourcePath)) {
              issues.push(`[重做预检失败] 源文件/文件夹不存在: ${sourcePath}`);
              continue;
            }

            // 检查目标位置是否已被占用
            if (await fs.pathExists(targetPath)) {
              issues.push(`[重做预检失败] 目标位置已被占用: ${targetPath}`);
              continue;
            }

            // 检查源文件的读取权限
            const copySourcePermCheck = await this.checkPermissions(sourcePath, 'read');
            if (!copySourcePermCheck.hasPermission) {
              issues.push(`[重做预检失败] 源文件/文件夹权限不足: ${sourcePath} - ${copySourcePermCheck.error}`);
              continue;
            }

            // 检查目标位置的写入权限
            const copyTargetDir = require('path').dirname(targetPath);
            const copyTargetPermCheck = await this.checkDirectoryPermissions(copyTargetDir);
            if (!copyTargetPermCheck.hasPermission) {
              issues.push(`[重做预检失败] 目标目录权限不足: ${copyTargetDir} - ${copyTargetPermCheck.error}`);
              continue;
            }
            break;

          case 'delete':
            // 删除操作的重做实际上是再次删除，通常不支持
            issues.push(`[重做预检警告] 删除操作无法重做: ${sourcePath}`);
            break;

          case 'createFolder':
            const folderPath = targetPath || sourcePath;
            if (!folderPath) {
              issues.push(`[重做预检失败] 创建文件夹记录无效: ${operation.originalName}`);
              continue;
            }

            // 检查文件夹是否已存在
            if (await fs.pathExists(folderPath)) {
              issues.push(`[重做预检失败] 文件夹已存在: ${folderPath}`);
              continue;
            }

            // 检查父目录的写入权限
            const parentDir = require('path').dirname(folderPath);
            const parentPermCheck = await this.checkDirectoryPermissions(parentDir);
            if (!parentPermCheck.hasPermission) {
              issues.push(`[重做预检失败] 父目录权限不足: ${parentDir} - ${parentPermCheck.error}`);
              continue;
            }
            break;
        }
      } catch (error) {
        issues.push(`重做预检查操作 ${operation.originalName} 时出错: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return {
      canRedo: issues.length === 0,
      issues
    };
  }

  /**
   * 预检查撤销操作的可行性 (支持连锁重命名检测)
   */
  private async preCheckUndoOperations(operations: FileOperation[]): Promise<{ canUndo: boolean; issues: string[] }> {
    const issues: string[] = [];
    const successfulOperations = operations.filter(op => op.status === 'success');

    // 检测连锁重命名冲突
    const chainConflicts = await this.detectChainRenameConflicts(successfulOperations);
    if (chainConflicts.length > 0) {
      issues.push(...chainConflicts);
    }

    // 检查磁盘空间
    const requiredSpace = this.calculateRequiredSpace(successfulOperations);
    if (requiredSpace > 0) {
      // 检查主要目标路径的磁盘空间
      const moveOperations = successfulOperations.filter(op =>
        (op.operation === 'move' || op.operation === 'rename') && op.originalPath
      );

      for (const operation of moveOperations.slice(0, 3)) { // 只检查前3个，避免过多检查
        const spaceCheck = await this.checkDiskSpace(operation.originalPath, requiredSpace);
        if (!spaceCheck.hasSpace) {
          issues.push(`[预检警告] ${spaceCheck.error}`);
          break; // 只报告一次磁盘空间问题
        }
      }
    }

    for (const operation of successfulOperations) {
      const sourcePath = operation.newPath;
      const finalDestPath = operation.originalPath;

      try {
        switch (operation.operation) {
          case 'move':
          case 'rename':
            if (!sourcePath || !finalDestPath) {
              issues.push(`[预检失败] 操作记录无效: ${operation.originalName}`);
              continue;
            }
            if (!await fs.pathExists(sourcePath)) {
              issues.push(`[预检失败] 源文件/夹不存在: ${sourcePath}`);
              continue;
            }
            // 对于连锁重命名，我们在上面已经检测过了，这里跳过简单的存在性检查
            if (await fs.pathExists(finalDestPath) && chainConflicts.length === 0) {
              issues.push(`[预检失败] 目标位置已被占用: ${finalDestPath}`);
              continue;
            }

            // 检查源文件的读取权限
            const sourcePermCheck = await this.checkPermissions(sourcePath, 'read');
            if (!sourcePermCheck.hasPermission) {
              issues.push(`[预检失败] 源文件权限不足: ${sourcePath} - ${sourcePermCheck.error}`);
              continue;
            }

            // 检查目标位置的写入权限
            const targetDir = require('path').dirname(finalDestPath);
            const targetPermCheck = await this.checkDirectoryPermissions(targetDir);
            if (!targetPermCheck.hasPermission) {
              issues.push(`[预检失败] 目标目录权限不足: ${targetDir} - ${targetPermCheck.error}`);
              continue;
            }
            break;

          case 'copy':
            if (!sourcePath) {
              issues.push(`[预检失败] 复制记录无效: ${operation.originalName}`);
              continue;
            }
            // 对于复制操作，只需要检查复制的文件是否存在（不存在也不是问题，只是警告）
            if (await fs.pathExists(sourcePath)) {
              // 检查是否有删除权限
              const deletePermCheck = await this.checkPermissions(sourcePath, 'write');
              if (!deletePermCheck.hasPermission) {
                issues.push(`[预检失败] 复制的文件/夹不可删除: ${sourcePath} - ${deletePermCheck.error}`);
              }
            }
            break;

          case 'delete':
            // 删除操作无法撤销，但不算作阻止撤销的问题
            break;

          case 'createFolder':
            const folderPath = operation.newPath || operation.originalPath;
            if (!folderPath) {
              issues.push(`[预检失败] 创建文件夹记录无效: ${operation.originalName}`);
              continue;
            }
            // 检查创建的文件夹是否存在（不存在也不是问题，只是警告）
            if (await fs.pathExists(folderPath)) {
              // 检查文件夹是否为空
              try {
                const items = await fs.readdir(folderPath);
                if (items.length > 0) {
                  issues.push(`[预检警告] 创建的文件夹不为空，无法撤销: ${folderPath}`);
                }
              } catch {
                issues.push(`[预检失败] 无法读取创建的文件夹: ${folderPath}`);
              }
            }
            break;
        }
      } catch (error) {
        issues.push(`检查操作 ${operation.originalName} 时出错: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return {
      canUndo: issues.length === 0,
      issues
    };
  }

  /**
   * 检测连锁重命名冲突
   */
  private async detectChainRenameConflicts(operations: FileOperation[]): Promise<string[]> {
    const conflicts: string[] = [];
    const renameOps = operations.filter(op => op.operation === 'rename' || op.operation === 'move');

    if (renameOps.length === 0) {
      return conflicts;
    }

    console.log(`🔍 检测连锁重命名冲突，共 ${renameOps.length} 个重命名操作`);

    // 构建撤回目标路径映射
    const undoTargets = new Map<string, FileOperation>();
    for (const op of renameOps) {
      if (op.originalPath) {
        undoTargets.set(op.originalPath, op);
      }
    }

    // 检查每个操作的撤回目标是否被其他文件占用
    for (const operation of renameOps) {
      const sourcePath = operation.newPath;
      const targetPath = operation.originalPath;

      if (!sourcePath || !targetPath) continue;

      // 检查目标位置是否存在文件
      if (await fs.pathExists(targetPath)) {
        // 检查占用该位置的文件是否也是本次工作流的产物
        const occupyingOp = renameOps.find(op => op.newPath === targetPath);

        if (occupyingOp) {
          // 这是一个连锁重命名冲突
          console.log(`⚠️ 检测到连锁重命名冲突:`);
          console.log(`  - 操作1: ${occupyingOp.originalPath} -> ${occupyingOp.newPath}`);
          console.log(`  - 操作2: ${operation.originalPath} -> ${operation.newPath}`);
          console.log(`  - 冲突: 操作2想要撤回到 ${targetPath}，但该位置被操作1的结果占用`);

          conflicts.push(`[连锁冲突] 无法撤回 ${operation.originalName}，因为目标位置 ${targetPath} 被同批次操作的文件占用。建议使用连锁撤回功能。`);
        } else {
          // 被其他文件占用（非本次工作流产物）
          conflicts.push(`[预检失败] 目标位置已被其他文件占用: ${targetPath}`);
        }
      }
    }

    return conflicts;
  }

  /**
   * 分析连锁依赖关系
   */
  private async analyzeChainDependencies(operations: FileOperation[]): Promise<{
    conflicts: Array<{ operation: FileOperation; blockingOperation: FileOperation }>;
    executionOrder: FileOperation[];
  }> {
    const conflicts: Array<{ operation: FileOperation; blockingOperation: FileOperation }> = [];
    const renameOps = operations.filter(op =>
      (op.operation === 'rename' || op.operation === 'move') && op.status === 'success'
    );

    // 构建依赖图
    for (const operation of renameOps) {
      const targetPath = operation.originalPath;
      if (!targetPath) continue;

      // 查找占用目标位置的操作
      const blockingOp = renameOps.find(op => op.newPath === targetPath);
      if (blockingOp && blockingOp !== operation) {
        conflicts.push({ operation, blockingOperation: blockingOp });
      }
    }

    // 计算执行顺序（拓扑排序）
    const executionOrder = this.calculateUndoOrder(renameOps, conflicts);

    return { conflicts, executionOrder };
  }

  /**
   * 计算撤回执行顺序
   */
  private calculateUndoOrder(
    operations: FileOperation[],
    conflicts: Array<{ operation: FileOperation; blockingOperation: FileOperation }>
  ): FileOperation[] {
    const order: FileOperation[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    // 构建依赖图
    const dependencies = new Map<string, string[]>();
    for (const op of operations) {
      dependencies.set(op.id, []);
    }

    for (const conflict of conflicts) {
      const deps = dependencies.get(conflict.operation.id) || [];
      deps.push(conflict.blockingOperation.id);
      dependencies.set(conflict.operation.id, deps);
    }

    // 深度优先搜索进行拓扑排序
    const dfs = (opId: string): boolean => {
      if (visiting.has(opId)) {
        // 检测到循环依赖
        console.warn(`检测到循环依赖: ${opId}`);
        return false;
      }
      if (visited.has(opId)) {
        return true;
      }

      visiting.add(opId);
      const deps = dependencies.get(opId) || [];

      for (const depId of deps) {
        if (!dfs(depId)) {
          return false;
        }
      }

      visiting.delete(opId);
      visited.add(opId);

      const operation = operations.find(op => op.id === opId);
      if (operation) {
        order.unshift(operation); // 添加到开头，因为依赖的操作需要先执行
      }

      return true;
    };

    // 对所有操作进行排序
    for (const operation of operations) {
      if (!visited.has(operation.id)) {
        dfs(operation.id);
      }
    }

    return order;
  }

  /**
   * 执行重做操作 (终极修复版：采用“偏执”的两步法)
   */
  private async performRedoOperations(operations: FileOperation[]): Promise<void> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 只重做成功的操作
    const successfulOperations = operations.filter(op => op.status === 'success');

    for (const operation of successfulOperations) {
      const sourcePath = operation.originalPath; // 重做时，源是原始路径
      const finalDestPath = operation.newPath;    // 目标是新路径
      let tempPath: string | undefined; // 声明临时路径变量

      try {
        switch (operation.operation) {
          case 'move':
          case 'rename':
            console.log(`[防御性重做] 准备执行: ${sourcePath} -> ${finalDestPath}`);
            if (!sourcePath || !finalDestPath) {
                errors.push(`重做失败：操作记录无效，路径缺失。`);
                continue;
            }
            if (!await fs.pathExists(sourcePath)) {
                errors.push(`重做失败：源文件/夹不存在: ${sourcePath}`);
                continue;
            }
            if (await fs.pathExists(finalDestPath)) {
                errors.push(`重做失败：目标位置已被占用: ${finalDestPath}`);
                continue;
            }

            const destParentDir = require('path').dirname(finalDestPath);
            await fs.ensureDir(destParentDir);

            // 采用与撤销完全相同的“偏执”两步法
            const tempName = `redo-temp-${operation.id}-${Date.now()}`;
            tempPath = require('path').join(destParentDir, tempName);

            await fs.move(sourcePath, tempPath!);
            await fs.rename(tempPath!, finalDestPath);
            console.log(`✅ 成功重做: ${sourcePath} -> ${finalDestPath}`);
            break;

          case 'copy':
            if (finalDestPath) {
              await fs.copy(sourcePath, finalDestPath);
              console.log(`✅ 成功重做复制: ${sourcePath} -> ${finalDestPath}`);
            }
            break;

          case 'delete':
            // 理论上重做“删除”需要将文件移到回收站，这是一个复杂且依赖平台的功能。
            // 目前简单地标记为无法重做是合理的。
            warnings.push(`删除操作无法自动重做: ${sourcePath}`);
            break;
        }
      } catch (err) {
        const errorMsg = `重做操作失败 ${operation.originalPath}: ${err instanceof Error ? err.message : String(err)}`;
        console.error(errorMsg, err);
        errors.push(errorMsg);

        // 如果发生错误，尝试清理残留的临时文件/文件夹。
        if (tempPath && await fs.pathExists(tempPath)) {
            await fs.remove(tempPath).catch(cleanupErr => {
                console.error(`!!! 清理临时文件中转失败: ${tempPath}`, cleanupErr);
            });
        }
      }
    }

    if (errors.length > 0) {
      let errorMessage = `重做过程中发生错误:\n${errors.join('\n')}`;
      if (warnings.length > 0) {
        errorMessage += `\n\n警告:\n${warnings.join('\n')}`;
      }
      throw new Error(errorMessage);
    } else if (warnings.length > 0) {
      console.warn(`重做完成，但有警告:\n${warnings.join('\n')}`);
    }
  }

  /**
   * 清理工作流执行过程中创建的文件夹
   */
  private async cleanupCreatedDirectories(createdDirectories: string[]): Promise<void> {
    if (!createdDirectories || createdDirectories.length === 0) {
      console.log('没有需要清理的工作流创建文件夹');
      return;
    }

    console.log(`开始清理 ${createdDirectories.length} 个工作流创建的文件夹...`);
    console.log('待清理的文件夹列表:', createdDirectories);

    // 按路径深度排序，从最深的开始清理
    const sortedDirs = [...createdDirectories].sort((a, b) => {
      const depthA = a.split(require('path').sep).length;
      const depthB = b.split(require('path').sep).length;
      return depthB - depthA; // 深度大的在前
    });

    let cleanedCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];

    for (const dirPath of sortedDirs) {
      try {
        // 检查目录是否存在
        if (!await fs.pathExists(dirPath)) {
          console.log(`📂 文件夹已不存在，跳过: ${dirPath}`);
          continue;
        }

        // 检查目录是否为空
        const items = await fs.readdir(dirPath);
        if (items.length === 0) {
          await fs.rmdir(dirPath);
          cleanedCount++;
          console.log(`✅ 已清理空文件夹: ${dirPath}`);
        } else {
          skippedCount++;
          console.log(`⚠️ 文件夹不为空，跳过清理: ${dirPath} (包含 ${items.length} 个项目: ${items.slice(0, 3).join(', ')}${items.length > 3 ? '...' : ''})`);

          // 对于非空文件夹，检查是否只包含我们创建的子文件夹
          const onlyContainsCreatedDirs = items.every(item => {
            const itemPath = require('path').join(dirPath, item);
            return createdDirectories.includes(itemPath);
          });

          if (onlyContainsCreatedDirs) {
            console.log(`🔍 文件夹 ${dirPath} 只包含工作流创建的子文件夹，将在子文件夹清理后重新检查`);
          }
        }
      } catch (error) {
        const errorMsg = `清理文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
        console.warn(errorMsg);
        errors.push(errorMsg);
      }
    }

    // 第二轮清理：清理在第一轮中变为空的文件夹
    console.log(`🔄 开始第二轮清理，检查是否有文件夹在第一轮清理后变为空...`);
    let secondRoundCleaned = 0;

    for (const dirPath of sortedDirs) {
      try {
        // 检查目录是否存在且为空
        if (await fs.pathExists(dirPath)) {
          const items = await fs.readdir(dirPath);
          if (items.length === 0) {
            await fs.rmdir(dirPath);
            secondRoundCleaned++;
            cleanedCount++;
            console.log(`✅ 第二轮清理空文件夹: ${dirPath}`);
          }
        }
      } catch (error) {
        const errorMsg = `第二轮清理文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
        console.warn(errorMsg);
        errors.push(errorMsg);
      }
    }

    if (secondRoundCleaned > 0) {
      console.log(`🎯 第二轮清理了 ${secondRoundCleaned} 个文件夹`);
    }

    // 详细的清理结果报告
    console.log(`📊 文件夹清理完成:`);
    console.log(`  - 成功清理: ${cleanedCount} 个空文件夹`);
    console.log(`  - 跳过清理: ${skippedCount} 个非空文件夹`);
    console.log(`  - 清理错误: ${errors.length} 个`);

    if (cleanedCount > 0) {
      console.log(`✅ 成功清理了 ${cleanedCount} 个工作流创建的空文件夹`);
    } else {
      console.log(`ℹ️ 没有发现需要清理的空文件夹`);
    }

    if (errors.length > 0) {
      console.warn(`清理过程中发生 ${errors.length} 个错误，但不影响撤销操作:`);
      errors.forEach(error => console.warn(`  - ${error}`));
    }
  }

  /**
   * 恢复被清理的空文件夹
   */
  private async restoreCleanedEmptyDirectories(cleanedEmptyDirectories: string[]): Promise<void> {
    if (!cleanedEmptyDirectories || cleanedEmptyDirectories.length === 0) {
      console.log('没有需要恢复的空文件夹');
      return;
    }

    console.log(`开始恢复 ${cleanedEmptyDirectories.length} 个被清理的空文件夹...`);
    console.log('待恢复的文件夹列表:', cleanedEmptyDirectories);

    // 按路径深度排序，从最浅的开始恢复（与清理顺序相反）
    const sortedDirs = [...cleanedEmptyDirectories].sort((a, b) => {
      const depthA = a.split(require('path').sep).length;
      const depthB = b.split(require('path').sep).length;
      return depthA - depthB; // 深度小的在前
    });

    let restoredCount = 0;
    let skippedCount = 0;
    const errors: string[] = [];

    for (const dirPath of sortedDirs) {
      try {
        // 检查目录是否已经存在
        if (await fs.pathExists(dirPath)) {
          console.log(`📂 文件夹已存在，跳过恢复: ${dirPath}`);
          skippedCount++;
          continue;
        }

        // 创建空文件夹
        await fs.ensureDir(dirPath);
        restoredCount++;
        console.log(`✅ 已恢复空文件夹: ${dirPath}`);
      } catch (error) {
        const errorMsg = `恢复空文件夹失败 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`;
        console.warn(errorMsg);
        errors.push(errorMsg);
      }
    }

    // 详细的恢复结果报告
    console.log(`📊 空文件夹恢复完成:`);
    console.log(`  - 成功恢复: ${restoredCount} 个空文件夹`);
    console.log(`  - 跳过恢复: ${skippedCount} 个已存在的文件夹`);
    console.log(`  - 恢复错误: ${errors.length} 个`);

    if (restoredCount > 0) {
      console.log(`✅ 成功恢复了 ${restoredCount} 个被清理的空文件夹`);
    } else {
      console.log(`ℹ️ 没有需要恢复的空文件夹或文件夹已存在`);
    }

    if (errors.length > 0) {
      console.warn(`恢复过程中发生 ${errors.length} 个错误，但不影响撤销操作:`);
      errors.forEach(error => console.warn(`  - ${error}`));
    }
  }

  /**
   * 清理部分完成的步骤
   */
  private async cleanupPartialStep(step: OperationStep): Promise<void> {
    console.log(`🧹 清理部分完成的步骤: ${step.type} - ${step.id}`);

    // 清理临时文件
    if (step.tempFiles && step.tempFiles.length > 0) {
      for (const tempFile of step.tempFiles) {
        try {
          if (await fs.pathExists(tempFile)) {
            await fs.remove(tempFile);
            console.log(`   🗑️ 清理临时文件: ${tempFile}`);
          }
        } catch (error) {
          console.warn(`   ⚠️ 清理临时文件失败: ${tempFile}`, error);
        }
      }
    }

    // 根据操作类型进行特定清理
    switch (step.type) {
      case 'file_move':
        // 如果移动操作部分完成，尝试恢复到原始状态
        if (step.targetPath && step.sourcePath) {
          if (await fs.pathExists(step.targetPath) && !await fs.pathExists(step.sourcePath)) {
            try {
              await fs.move(step.targetPath, step.sourcePath);
              console.log(`   ↩️ 恢复部分移动的文件: ${step.targetPath} -> ${step.sourcePath}`);
            } catch (error) {
              console.warn(`   ⚠️ 恢复部分移动失败`, error);
            }
          }
        }
        break;
      case 'file_copy':
        // 如果复制操作部分完成，删除部分复制的文件
        if (step.targetPath && await fs.pathExists(step.targetPath)) {
          try {
            await fs.remove(step.targetPath);
            console.log(`   🗑️ 删除部分复制的文件: ${step.targetPath}`);
          } catch (error) {
            console.warn(`   ⚠️ 删除部分复制失败`, error);
          }
        }
        break;
      case 'folder_create':
        // 如果文件夹创建部分完成，尝试删除空文件夹
        if (step.targetPath && await fs.pathExists(step.targetPath)) {
          try {
            const items = await fs.readdir(step.targetPath);
            if (items.length === 0) {
              await fs.rmdir(step.targetPath);
              console.log(`   🗑️ 删除部分创建的空文件夹: ${step.targetPath}`);
            }
          } catch (error) {
            console.warn(`   ⚠️ 删除部分创建的文件夹失败`, error);
          }
        }
        break;
    }
  }

  /**
   * 中止正在进行的步骤
   */
  private async abortInProgressStep(step: OperationStep): Promise<void> {
    console.log(`⏹️ 中止正在进行的步骤: ${step.type} - ${step.id}`);

    // 标记步骤为已中止
    step.inProgress = false;
    step.partiallyCompleted = true;

    // 执行清理操作
    await this.cleanupPartialStep(step);
  }

  /**
   * 清理未开始的步骤
   */
  private async cleanupUnstartedStep(step: OperationStep): Promise<void> {
    console.log(`🗑️ 清理未开始的步骤: ${step.type} - ${step.id}`);

    // 清理可能预分配的资源
    if (step.tempFiles && step.tempFiles.length > 0) {
      for (const tempFile of step.tempFiles) {
        try {
          if (await fs.pathExists(tempFile)) {
            await fs.remove(tempFile);
            console.log(`   🗑️ 清理预分配的临时文件: ${tempFile}`);
          }
        } catch (error) {
          console.warn(`   ⚠️ 清理预分配文件失败: ${tempFile}`, error);
        }
      }
    }

    // 清理可能的元数据
    if (step.metadata) {
      console.log(`   📋 清理步骤元数据: ${JSON.stringify(step.metadata)}`);
    }
  }

  /**
   * 创建操作进度跟踪器
   */
  private createProgressTracker(operationName: string, totalSteps: number): {
    updateProgress: (currentStep: number, stepName: string, status?: 'success' | 'error' | 'warning') => void;
    complete: (summary: { success: number; errors: number; warnings: number }) => void;
    error: (error: string) => void;
  } {
    const startTime = Date.now();

    return {
      updateProgress: (currentStep: number, stepName: string, status: 'success' | 'error' | 'warning' = 'success') => {
        const percentage = Math.round((currentStep / totalSteps) * 100);
        const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️';
        const elapsed = Date.now() - startTime;
        const estimatedTotal = totalSteps > 0 ? (elapsed / currentStep) * totalSteps : 0;
        const remaining = Math.max(0, estimatedTotal - elapsed);

        console.log(`[${operationName}] ${statusIcon} ${percentage}% (${currentStep}/${totalSteps}) - ${stepName}`);

        if (remaining > 1000) {
          const remainingSeconds = Math.round(remaining / 1000);
          console.log(`   ⏱️ 预计剩余时间: ${remainingSeconds}秒`);
        }
      },

      complete: (summary: { success: number; errors: number; warnings: number }) => {
        const totalTime = Date.now() - startTime;
        const timeStr = totalTime > 1000 ? `${(totalTime / 1000).toFixed(1)}秒` : `${totalTime}毫秒`;

        console.log(`\n🎯 ${operationName}完成 - 用时: ${timeStr}`);
        console.log(`   ✅ 成功: ${summary.success}个`);
        if (summary.warnings > 0) {
          console.log(`   ⚠️ 警告: ${summary.warnings}个`);
        }
        if (summary.errors > 0) {
          console.log(`   ❌ 错误: ${summary.errors}个`);
        }
      },

      error: (error: string) => {
        const totalTime = Date.now() - startTime;
        const timeStr = totalTime > 1000 ? `${(totalTime / 1000).toFixed(1)}秒` : `${totalTime}毫秒`;

        console.error(`\n💥 ${operationName}失败 - 用时: ${timeStr}`);
        console.error(`   错误: ${error}`);
      }
    };
  }

  /**
   * 增强的用户反馈机制
   */
  private generateUserFeedback(
    operation: string,
    result: { success: boolean; errors?: string[]; warnings?: string[] },
    context?: { entryId?: string; fileCount?: number; duration?: number }
  ): {
    title: string;
    message: string;
    type: 'success' | 'warning' | 'error';
    actions?: Array<{ label: string; action: string }>;
  } {
    const errors = result.errors || [];
    const warnings = result.warnings || [];

    if (result.success && errors.length === 0 && warnings.length === 0) {
      // 完全成功
      return {
        title: `${operation}成功`,
        message: context?.fileCount
          ? `成功处理了 ${context.fileCount} 个文件${context.duration ? `，用时 ${(context.duration / 1000).toFixed(1)} 秒` : ''}`
          : `${operation}操作已成功完成`,
        type: 'success'
      };
    } else if (result.success && warnings.length > 0 && errors.length === 0) {
      // 成功但有警告
      return {
        title: `${operation}完成（有警告）`,
        message: `操作已完成，但有 ${warnings.length} 个警告需要注意：\n${warnings.slice(0, 3).join('\n')}${warnings.length > 3 ? '\n...' : ''}`,
        type: 'warning',
        actions: [
          { label: '查看详细信息', action: 'show_details' },
          { label: '忽略警告', action: 'dismiss' }
        ]
      };
    } else if (!result.success && errors.length > 0) {
      // 失败
      const errorReport = this.createErrorReport(errors, { operation, entryId: context?.entryId });

      return {
        title: `${operation}失败`,
        message: `${errorReport.summary}\n\n主要问题：\n${errorReport.details.slice(0, 2).join('\n')}${errorReport.details.length > 2 ? '\n...' : ''}`,
        type: 'error',
        actions: errorReport.canRetry
          ? [
              { label: '重试', action: 'retry' },
              { label: '查看解决方案', action: 'show_solutions' },
              { label: '手动处理', action: 'manual_fix' }
            ]
          : [
              { label: '查看解决方案', action: 'show_solutions' },
              { label: '手动处理', action: 'manual_fix' }
            ]
      };
    } else {
      // 部分成功
      return {
        title: `${operation}部分完成`,
        message: `操作部分完成，有 ${errors.length} 个错误和 ${warnings.length} 个警告`,
        type: 'warning',
        actions: [
          { label: '查看详细信息', action: 'show_details' },
          { label: '重试失败项', action: 'retry_failed' }
        ]
      };
    }
  }
}

// 导出单例实例
const historyManager = new HistoryManager();

module.exports = { HistoryManager, historyManager };