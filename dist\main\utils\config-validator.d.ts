import type { Workflow, ProcessStep, MonitorTask } from '@shared/types';
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
/**
 * 验证工作流配置
 */
export declare function validateWorkflow(workflow: Workflow): ValidationResult;
/**
 * 验证工作流步骤
 */
export declare function validateWorkflowStep(step: ProcessStep, stepIndex: number): ValidationResult;
/**
 * 验证动作配置
 */
export declare function validateActionConfig(config: any, actionType: string, stepIndex: number, actionIndex: number): ValidationResult;
/**
 * 验证监控任务配置
 */
export declare function validateMonitorTask(task: Partial<MonitorTask>): ValidationResult;
/**
 * 验证路径安全性
 */
export declare function validatePathSafety(filePath: string): ValidationResult;
