import { EventEmitter } from 'events';
import { MonitorTask, MonitorExecutionResult, MonitorTaskStatus, MonitorErrorRecord, Workflow } from '../../shared/types';
import { WorkflowEngine } from './workflow-engine';
import { HistoryManager } from './history-manager';
export declare class MonitorManager extends EventEmitter {
    private tasks;
    private watchers;
    private cronJobs;
    private runningExecutions;
    private fileQueues;
    private watcherCleanups;
    private workflowEngine;
    private historyManager;
    private store;
    private getWorkflowFn;
    constructor(workflowEngine: WorkflowEngine, historyManager: HistoryManager, store: any, getWorkflowFn: (workflowId: string) => Promise<Workflow | null>);
    /**
     * 初始化监控管理器
     */
    initialize(): Promise<void>;
    /**
     * 加载所有监控任务
     */
    private loadTasks;
    /**
     * 保存所有监控任务
     */
    private saveTasks;
    /**
     * 创建新的监控任务
     */
    createTask(taskData: Omit<MonitorTask, 'id' | 'createdAt' | 'updatedAt' | 'statistics'>): Promise<MonitorTask>;
    /**
     * 更新监控任务
     */
    updateTask(taskId: string, updates: Partial<MonitorTask>): Promise<MonitorTask | null>;
    /**
     * 删除监控任务
     */
    deleteTask(taskId: string): Promise<boolean>;
    /**
     * 获取所有监控任务
     */
    getAllTasks(): MonitorTask[];
    /**
     * 获取单个监控任务
     */
    getTask(taskId: string): MonitorTask | null;
    /**
     * 启动监控任务
     */
    startTask(taskId: string): Promise<void>;
    /**
     * 停止监控任务
     */
    stopTask(taskId: string): Promise<void>;
    /**
     * 启动文件监控
     */
    private startFileWatcher;
    /**
     * 启动定时任务
     */
    private startScheduledTask;
    /**
     * 处理文件变化
     */
    private handleFileChanges;
    /**
     * 添加文件到待处理队列
     */
    private addToFileQueue;
    /**
     * 处理队列中的文件
     */
    private processQueuedFiles;
    /**
     * 执行定时任务
     */
    private executeScheduledTask;
    /**
     * 从路径收集文件
     */
    private collectFilesFromPath;
    /**
     * 检查文件是否应该被忽略
     */
    private shouldIgnoreFile;
    /**
     * 简单的通配符模式匹配
     */
    private matchPattern;
    /**
     * 根据监控事件过滤文件
     * 对于定时任务，我们需要模拟文件事件的概念
     */
    private filterFilesByEvents;
    /**
     * 执行任务（通用方法）
     */
    private executeTaskWithFiles;
    /**
     * 手动执行监控任务
     */
    executeTask(taskId: string, filePaths?: string[]): Promise<MonitorExecutionResult>;
    /**
     * 获取任务状态
     */
    getTaskStatus(taskId: string): MonitorTaskStatus | null;
    /**
     * 获取所有任务状态
     */
    getAllTaskStatuses(): MonitorTaskStatus[];
    /**
     * 获取工作流
     */
    private getWorkflow;
    /**
     * 获取文件类型
     */
    private getFileType;
    /**
     * 获取下次执行时间
     */
    private getNextExecutionTime;
    /**
     * 记录增强的错误信息
     */
    private recordEnhancedError;
    /**
     * 记录成功执行
     */
    private recordSuccessfulExecution;
    /**
     * 获取任务的错误统计摘要
     */
    getTaskErrorSummary(taskId: string): {
        totalErrors: number;
        errorsByType: Record<string, number>;
        recentErrors: MonitorErrorRecord[];
        consecutiveFailures: number;
        lastSuccessTime?: string;
    } | null;
    /**
     * 获取所有任务的错误概览
     */
    getAllTasksErrorOverview(): Array<{
        taskId: string;
        taskName: string;
        status: string;
        consecutiveFailures: number;
        lastError?: string;
        lastErrorTime?: string;
    }>;
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
}
