"use strict";
// src/shared/types.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileChangeType = void 0;
// 文件变化类型枚举
var FileChangeType;
(function (FileChangeType) {
    FileChangeType["RENAMED"] = "renamed";
    FileChangeType["MOVED"] = "moved";
    FileChangeType["MODIFIED"] = "modified";
    FileChangeType["DELETED"] = "deleted";
    FileChangeType["COPIED"] = "copied";
    FileChangeType["CREATED"] = "created"; // 创建了新的文件或文件夹
})(FileChangeType || (exports.FileChangeType = FileChangeType = {}));
